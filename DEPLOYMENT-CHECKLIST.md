# PDF Fix - Deployment Checklist

## ✅ What Was Fixed

### Root Cause
PDFs were redirecting to the home page because:
1. The SPA router was intercepting PDF requests
2. Server configuration wasn't properly excluding `/assets/` paths
3. Missing proper MIME type handling for PDFs with spaces in filenames

### Solution Applied
1. **Vite Config**: Keep original filenames with spaces (browsers handle URL encoding automatically)
2. **Server Config**: Explicitly exclude `/assets/` from SPA fallback routing
3. **Build Process**: Ensure PDFs maintain proper extensions and create fallback copies

## 📋 Pre-Deployment Checklist

Before deploying to your live site, verify:

- [x] Build completed successfully with `npm run deploy:fix`
- [x] All 24 PDFs present in dist folder (7 current + 5 archive + 12 fallback)
- [x] PDF paths in JavaScript use URL encoding (e.g., `Abhishek%20Kumar.pdf`)
- [x] Actual PDF files have spaces in names (e.g., `Abhishek Kumar.pdf`)
- [x] `.htaccess` file present in dist folder
- [x] `_redirects` file present in dist folder
- [x] `vercel.json` in project root

## 🚀 Deployment Steps

### For Apache/cPanel/Shared Hosting

1. **Upload Files**
   ```
   - Upload ALL contents of the `dist` folder to your web root (public_html)
   - Make sure .htaccess is uploaded (enable "Show hidden files" in your FTP client)
   ```

2. **Verify .htaccess**
   - Check that `.htaccess` exists in your web root
   - Verify it contains the rule: `RewriteCond %{REQUEST_URI} !^/assets/`

3. **Set File Permissions**
   ```
   - Directories: 755
   - Files: 644
   - PDFs: 644
   ```

4. **Test PDF Access**
   - Try accessing: `https://yourdomain.com/assets/current/Aastha%20Bhatia.pdf`
   - Try accessing: `https://yourdomain.com/assets/archive/Volume-11-Issue-1-2024.pdf`

### For Vercel

1. **Verify vercel.json**
   ```bash
   # Make sure vercel.json is in your project root (not in dist)
   ```

2. **Deploy**
   ```bash
   # Option 1: Using Vercel CLI
   vercel --prod
   
   # Option 2: Push to Git (if connected)
   git add .
   git commit -m "Fix PDF routing issue"
   git push origin main
   ```

3. **After Deployment**
   - Wait 1-2 minutes for deployment to complete
   - Clear browser cache (Ctrl+Shift+Delete)
   - Test PDF links

### For Netlify

1. **Deploy**
   ```bash
   # Option 1: Drag and drop dist folder to Netlify dashboard
   
   # Option 2: Using Netlify CLI
   netlify deploy --prod --dir=dist
   
   # Option 3: Push to Git (if connected)
   git add .
   git commit -m "Fix PDF routing issue"
   git push origin main
   ```

2. **Verify _redirects**
   - Check deployment logs to ensure `_redirects` was processed
   - Look for: "Processing redirects file"

3. **After Deployment**
   - Clear browser cache
   - Test PDF links

## 🔍 Testing After Deployment

### Test URLs (Replace `yourdomain.com` with your actual domain)

**Current Issue PDFs:**
```
https://yourdomain.com/assets/current/Aastha%20Bhatia.pdf
https://yourdomain.com/assets/current/Abhishek%20Kumar.pdf
https://yourdomain.com/assets/current/Amit%20Kumar%20Singh.pdf
https://yourdomain.com/assets/current/Kashish%20Jain.pdf
https://yourdomain.com/assets/current/Murari%20Kumar.pdf
https://yourdomain.com/assets/current/Satyendra%20Narayan%20Singh.pdf
https://yourdomain.com/assets/current/Sharmishtha.pdf
```

**Archive PDFs:**
```
https://yourdomain.com/assets/archive/Volume-11-Issue-2-2024.pdf
https://yourdomain.com/assets/archive/Volume-11-Issue-1-2024.pdf
https://yourdomain.com/assets/archive/Volume-10-Issue-1-June-2023.pdf
https://yourdomain.com/assets/archive/Volume-10,%20Issue-2,%20December%202023.pdf
https://yourdomain.com/assets/archive/Volume-9-Issue-1-2022.pdf
```

**Fallback URLs (if above don't work):**
```
https://yourdomain.com/assets/Aastha%20Bhatia.pdf
https://yourdomain.com/assets/Volume-11-Issue-1-2024.pdf
```

### Expected Behavior
✅ PDF should open in browser or download
✅ Should NOT redirect to home page
✅ Should NOT show 404 error

## 🐛 Troubleshooting

### Issue: PDFs still redirect to home page

**For Apache/cPanel:**
1. Check if `.htaccess` file exists in web root
2. Verify Apache has `mod_rewrite` enabled (contact hosting support)
3. Check file permissions: `.htaccess` should be 644
4. Try adding this to the top of `.htaccess`:
   ```apache
   Options +FollowSymLinks
   ```

**For Vercel:**
1. Check deployment logs for errors
2. Verify `vercel.json` is in project root (not in dist)
3. Redeploy: `vercel --prod --force`

**For Netlify:**
1. Check if `_redirects` file was processed (check deploy logs)
2. Try adding `!` to force rule: `/assets/*.pdf /assets/:splat 200!`
3. Redeploy

### Issue: 404 Error on PDFs

1. **Check if files exist:**
   ```bash
   # On your server, verify files are present
   ls -la public_html/assets/current/
   ls -la public_html/assets/archive/
   ```

2. **Check file permissions:**
   ```bash
   # Files should be readable (644)
   chmod 644 public_html/assets/current/*.pdf
   chmod 644 public_html/assets/archive/*.pdf
   ```

3. **Check URL encoding:**
   - Spaces in URLs should be `%20`
   - Example: `Aastha Bhatia.pdf` → `Aastha%20Bhatia.pdf`

### Issue: PDF downloads instead of opening in browser

This is browser-dependent behavior. To force opening in browser, add this to `.htaccess`:
```apache
<FilesMatch "\.pdf$">
  Header set Content-Disposition "inline"
</FilesMatch>
```

### Issue: Works on localhost but not on live site

1. **Clear browser cache completely:**
   - Chrome: Ctrl+Shift+Delete → Clear all cached images and files
   - Or use Incognito mode

2. **Check server configuration:**
   - Ensure server supports `.htaccess` (Apache) or routing rules (Vercel/Netlify)
   - Contact hosting support if needed

3. **Verify correct build was uploaded:**
   - Re-run: `npm run deploy:fix`
   - Upload the NEW dist folder contents

## 📝 Important Notes

### URL Encoding
- Browsers automatically convert spaces to `%20` in URLs
- Your JavaScript references: `Abhishek%20Kumar.pdf`
- Actual filename on server: `Abhishek Kumar.pdf`
- This is CORRECT and expected behavior

### File Structure
Your dist folder should look like this:
```
dist/
├── assets/
│   ├── current/
│   │   ├── Aastha Bhatia.pdf
│   │   ├── Abhishek Kumar.pdf
│   │   └── ... (7 files total)
│   ├── archive/
│   │   ├── Volume-11-Issue-2-2024.pdf
│   │   └── ... (5 files total)
│   └── [Fallback copies of all PDFs]
├── .htaccess
├── _redirects
└── index.html
```

### Server Requirements
- **Apache**: Requires `mod_rewrite` enabled
- **Vercel**: No special requirements (uses vercel.json)
- **Netlify**: No special requirements (uses _redirects)

## ✅ Final Verification

After deployment, verify these 3 things:

1. **PDF Opens Correctly**
   - Click on any article in Current Issue
   - PDF should open in new tab (not redirect to home)

2. **Archive PDFs Work**
   - Navigate to Archive Articles
   - Click on any volume
   - PDF should open (not 404)

3. **Direct URL Access**
   - Copy a PDF URL from your site
   - Open in new incognito window
   - Should load PDF directly

## 🆘 Still Having Issues?

If PDFs still don't work after following all steps:

1. **Share these details:**
   - Hosting provider (Vercel/Netlify/cPanel/etc.)
   - Error message or behavior you're seeing
   - One example PDF URL that's not working
   - Browser console errors (F12 → Console tab)

2. **Check browser console:**
   - Press F12
   - Go to Console tab
   - Look for any red errors
   - Share the error messages

3. **Check Network tab:**
   - Press F12
   - Go to Network tab
   - Click on a PDF link
   - Check the response status (should be 200, not 301/302/404)

---

## 📤 Ready to Deploy?

Run this command and upload the `dist` folder:
```bash
npm run deploy:fix
```

Then follow the deployment steps for your hosting platform above.
