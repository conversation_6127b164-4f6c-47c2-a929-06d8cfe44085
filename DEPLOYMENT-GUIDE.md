# LLDJPS Deployment Guide

## 🚀 Quick Deployment

### For Production Deployment:
```bash
npm run build:deploy
```

This command will:
- Clean previous build
- Build the project with Vite
- Copy all PDF files to the correct location
- Copy .htaccess file for proper routing
- Verify the build integrity

### Manual Deployment Steps:
1. Run the build command above
2. Upload the entire `dist` folder contents to your web server
3. Ensure your web server supports `.htaccess` files (Apache)

## 🔧 Fixes Applied for Content Persistence

### 1. **Enhanced .htaccess Configuration**
- Added specific route handling for `/repository/current`
- Improved SPA routing support
- Added PDF file access protection
- Configured proper caching headers

### 2. **Robust Component Architecture**
- Added Error Boundary for better error handling
- Implemented loading states
- Created persistent data structure in `src/constants/currentIssueData.js`
- Added fallback mechanisms for data loading

### 3. **Build Process Improvements**
- Custom build script ensures all assets are copied
- Proper Vite configuration for SPA routing
- Automatic PDF file copying to dist folder
- Build verification process

### 4. **Routing Enhancements**
- Added duplicate routes for `/repository/current` and `/repository/current/`
- Improved route handling in App.jsx
- Better fallback routing

## 📁 File Structure After Build

```
dist/
├── index.html
├── .htaccess                 # SPA routing configuration
├── assets/
│   ├── current/             # PDF files for current issue
│   │   ├── Aastha Bhatia.pdf
│   │   ├── Abhishek Kumar.pdf
│   │   └── ... (all PDFs)
│   ├── [other-assets]       # CSS, JS, images
│   └── ...
└── ...
```

## 🌐 Server Requirements

### Apache Server:
- Ensure `mod_rewrite` is enabled
- Allow `.htaccess` files in directory configuration
- Ensure PDF MIME type is configured

### Nginx Server:
If using Nginx, add this configuration:
```nginx
location / {
    try_files $uri $uri/ /index.html;
}

location /assets/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 🔍 Troubleshooting

### If content still disappears after refresh:

1. **Check .htaccess is working:**
   - Verify `.htaccess` file exists in root directory
   - Ensure server supports `.htaccess` files
   - Check Apache `mod_rewrite` is enabled

2. **Verify PDF files:**
   - Check `dist/assets/current/` contains all PDF files
   - Verify PDF files are accessible via direct URL

3. **Browser Console:**
   - Check for JavaScript errors
   - Verify network requests are successful
   - Check if routing is working properly

4. **Server Configuration:**
   - Ensure server serves `index.html` for all routes
   - Check MIME types are configured correctly
   - Verify CORS settings if needed

## 📋 Pre-Deployment Checklist

- [ ] Run `npm run build:deploy` successfully
- [ ] Verify all 7 PDF files are in `dist/assets/current/`
- [ ] Check `.htaccess` file exists in `dist/`
- [ ] Test locally with `npm run preview`
- [ ] Verify no console errors
- [ ] Test refresh functionality on `/repository/current`

## 🎯 Key Features Implemented

1. **Data Persistence:** Articles data is stored in constants file
2. **Error Handling:** Comprehensive error boundaries and fallbacks
3. **Loading States:** Proper loading indicators
4. **Responsive Design:** Works on all screen sizes
5. **PDF Access:** All PDFs properly linked and accessible
6. **SEO Friendly:** Proper routing and meta tags
7. **Performance:** Optimized build with code splitting

## 📞 Support

If you encounter any issues:
1. Check the browser console for errors
2. Verify server configuration
3. Ensure all files are uploaded correctly
4. Test the build locally first with `npm run preview`
