# PDF Deployment Guide - LLDJPS

## Problem Solved
This guide addresses the 404 error that occurs when trying to access PDF files after deployment. The issue was caused by improper asset handling during the build process.

## Solution Overview
The fix ensures PDFs are accessible through multiple paths and properly configured for different hosting platforms.

## Quick Fix Commands

### For New Deployments
```bash
npm run deploy:fix
```

### For Regular Builds
```bash
npm run build:deploy
```

## What the Fix Does

### 1. **Multiple PDF Access Paths**
PDFs are now accessible via:
- `/assets/current/filename.pdf` (organized structure)
- `/assets/archive/filename.pdf` (organized structure)  
- `/assets/filename.pdf` (fallback for direct access)

### 2. **Comprehensive Asset Copying**
- Copies PDFs from `public/assets/` directory
- Copies PDFs from `src/assets/` directory (imported assets)
- Creates fallback copies in root assets directory
- Maintains proper directory structure

### 3. **Server Configuration**
- **Apache (.htaccess)**: Handles PDF serving and routing
- **Netlify/Vercel (_redirects)**: Ensures proper routing for SPA
- **Vercel (vercel.json)**: Optimized for Vercel deployment

### 4. **Build Optimization**
- Vite configuration updated to handle PDFs properly
- PDFs maintain original names (no hashing for easier access)
- Proper MIME type handling

## File Structure After Fix

```
dist/
├── assets/
│   ├── current/
│   │   ├── Aastha Bhatia.pdf
│   │   ├── Abhishek Kumar.pdf
│   │   ├── Amit Kumar Singh.pdf
│   │   ├── Kashish Jain.pdf
│   │   ├── Murari Kumar.pdf
│   │   ├── Satyendra Narayan Singh.pdf
│   │   └── Sharmishtha.pdf
│   ├── archive/
│   │   ├── Volume-10-Issue-1-June-2023.pdf
│   │   ├── Volume-10, Issue-2, December 2023.pdf
│   │   ├── Volume-11-Issue-1-2024.pdf
│   │   ├── Volume-11-Issue-2-2024.pdf
│   │   └── Volume-9-Issue-1-2022.pdf
│   ├── Aastha Bhatia.pdf (fallback)
│   ├── Abhishek Kumar.pdf (fallback)
│   └── ... (all other PDFs as fallbacks)
├── .htaccess
├── _redirects
└── index.html
```

## Deployment Instructions

### For Vercel
1. Run: `npm run deploy:fix`
2. Deploy the `dist` folder
3. The `vercel.json` configuration will handle routing

### For Netlify
1. Run: `npm run deploy:fix`
2. Deploy the `dist` folder
3. The `_redirects` file will handle routing

### For Apache/Shared Hosting
1. Run: `npm run deploy:fix`
2. Upload the `dist` folder contents to your web root
3. The `.htaccess` file will handle routing

### For Other Platforms
1. Run: `npm run deploy:fix`
2. Deploy the `dist` folder
3. Ensure your platform supports the routing rules

## Testing PDF Access

After deployment, test these URLs:
- `https://yourdomain.com/assets/current/Aastha%20Bhatia.pdf`
- `https://yourdomain.com/assets/archive/Volume-11-Issue-1-2024.pdf`
- `https://yourdomain.com/assets/Aastha%20Bhatia.pdf` (fallback)

## Troubleshooting

### If PDFs Still Show 404:
1. Check if the deployment used the correct build command
2. Verify the `dist` folder contains the PDF files
3. Check server configuration for PDF handling
4. Ensure proper MIME types are set

### For Different Hosting Platforms:
- **GitHub Pages**: May need additional configuration
- **Firebase Hosting**: Update `firebase.json` with proper rewrites
- **AWS S3**: Ensure proper content types and CORS settings

## Code Changes Made

### 1. Updated `vite.config.js`
- Custom asset file naming for PDFs
- Proper directory structure for imported assets

### 2. Updated `src/constants/currentIssueData.js`
- Changed from string paths to ES6 imports
- Ensures proper asset processing by Vite

### 3. Enhanced `.htaccess`
- Better PDF handling
- Proper MIME type setting
- Fallback routing

### 4. Updated `vercel.json`
- Specific PDF routing rules
- Proper content type headers

### 5. Created `deploy-fix.js`
- Comprehensive deployment script
- Multiple fallback strategies
- Cross-platform compatibility

## Maintenance

### Adding New PDFs:
1. Add PDF to `public/assets/current/` or `public/assets/archive/`
2. Import in the appropriate data file
3. Run `npm run deploy:fix`
4. Deploy

### Updating Existing PDFs:
1. Replace the PDF file in `public/assets/`
2. Run `npm run deploy:fix`
3. Deploy

## Support

If you encounter issues:
1. Check the build output for any errors
2. Verify all PDF files are present in the `dist` folder
3. Test PDF access directly via URL
4. Check server logs for any routing issues

The fix ensures maximum compatibility across different hosting platforms and provides multiple fallback strategies for PDF access.
