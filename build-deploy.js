#!/usr/bin/env node

/**
 * Build and Deploy Script for LLDJPS
 * This script ensures proper build and asset copying for deployment
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Starting LLDJPS build process...');

try {
  // Step 1: Clean previous build
  console.log('🧹 Cleaning previous build...');
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
  }

  // Step 2: Run Vite build
  console.log('📦 Building with Vite...');
  execSync('npm run build', { stdio: 'inherit' });

  // Step 3: Ensure assets directories exist
  console.log('📁 Creating assets directories...');
  const currentAssetsDir = path.join('dist', 'assets', 'current');
  const archiveAssetsDir = path.join('dist', 'assets', 'archive');
  
  if (!fs.existsSync(currentAssetsDir)) {
    fs.mkdirSync(currentAssetsDir, { recursive: true });
  }
  if (!fs.existsSync(archiveAssetsDir)) {
    fs.mkdirSync(archiveAssetsDir, { recursive: true });
  }

  // Step 4: Copy PDF files from public directory
  console.log('📄 Copying PDF files from public directory...');
  const publicCurrentDir = path.join('public', 'assets', 'current');
  const publicArchiveDir = path.join('public', 'assets', 'archive');
  
  // Copy current issue PDFs
  if (fs.existsSync(publicCurrentDir)) {
    const files = fs.readdirSync(publicCurrentDir);
    files.forEach(file => {
      if (file.endsWith('.pdf')) {
        const sourcePath = path.join(publicCurrentDir, file);
        const destPath = path.join(currentAssetsDir, file);
        fs.copyFileSync(sourcePath, destPath);
        console.log(`  ✅ Copied current/${file}`);
      }
    });
  }

  // Copy archive PDFs
  if (fs.existsSync(publicArchiveDir)) {
    const files = fs.readdirSync(publicArchiveDir);
    files.forEach(file => {
      if (file.endsWith('.pdf')) {
        const sourcePath = path.join(publicArchiveDir, file);
        const destPath = path.join(archiveAssetsDir, file);
        fs.copyFileSync(sourcePath, destPath);
        console.log(`  ✅ Copied archive/${file}`);
      }
    });
  }

  // Step 5: Copy PDFs from src/assets (imported assets)
  console.log('📄 Copying imported PDF assets...');
  const srcCurrentDir = path.join('src', 'assets', 'current');
  const srcArchiveDir = path.join('src', 'assets', 'Archive Article');
  
  // Copy current issue PDFs from src
  if (fs.existsSync(srcCurrentDir)) {
    const files = fs.readdirSync(srcCurrentDir);
    files.forEach(file => {
      if (file.endsWith('.pdf')) {
        const sourcePath = path.join(srcCurrentDir, file);
        const destPath = path.join(currentAssetsDir, file);
        // Only copy if not already exists (avoid overwriting)
        if (!fs.existsSync(destPath)) {
          fs.copyFileSync(sourcePath, destPath);
          console.log(`  ✅ Copied src current/${file}`);
        }
      }
    });
  }

  // Copy archive PDFs from src
  if (fs.existsSync(srcArchiveDir)) {
    const files = fs.readdirSync(srcArchiveDir);
    files.forEach(file => {
      if (file.endsWith('.pdf')) {
        const sourcePath = path.join(srcArchiveDir, file);
        const destPath = path.join(archiveAssetsDir, file);
        // Only copy if not already exists (avoid overwriting)
        if (!fs.existsSync(destPath)) {
          fs.copyFileSync(sourcePath, destPath);
          console.log(`  ✅ Copied src archive/${file}`);
        }
      }
    });
  }

  // Step 6: Copy .htaccess file
  console.log('⚙️ Copying .htaccess file...');
  const htaccessSource = path.join('public', '.htaccess');
  const htaccessDest = path.join('dist', '.htaccess');
  if (fs.existsSync(htaccessSource)) {
    fs.copyFileSync(htaccessSource, htaccessDest);
    console.log('  ✅ .htaccess copied');
  }

  // Step 7: Verify build
  console.log('🔍 Verifying build...');
  const indexPath = path.join('dist', 'index.html');
  if (!fs.existsSync(indexPath)) {
    throw new Error('index.html not found in dist directory');
  }

  const currentPdfCount = fs.existsSync(currentAssetsDir) ? 
    fs.readdirSync(currentAssetsDir).filter(f => f.endsWith('.pdf')).length : 0;
  const archivePdfCount = fs.existsSync(archiveAssetsDir) ? 
    fs.readdirSync(archiveAssetsDir).filter(f => f.endsWith('.pdf')).length : 0;
  
  console.log(`  ✅ Found ${currentPdfCount} current issue PDF files`);
  console.log(`  ✅ Found ${archivePdfCount} archive PDF files`);

  console.log('🎉 Build completed successfully!');
  console.log('📂 Files ready for deployment in ./dist directory');
  
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
