#!/usr/bin/env node

/**
 * PDF Deployment Fix Script
 * This script ensures all PDFs are accessible after deployment
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 Starting PDF deployment fix...');

try {
  // Step 1: Clean and rebuild
  console.log('🧹 Cleaning previous build...');
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
  }

  // Step 2: Run Vite build
  console.log('📦 Building with Vite...');
  execSync('npm run build', { stdio: 'inherit' });

  // Step 3: Create comprehensive asset structure
  console.log('📁 Creating comprehensive asset structure...');
  
  const assetDirs = [
    'dist/assets',
    'dist/assets/current',
    'dist/assets/archive'
  ];
  
  assetDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });

  // Step 4: Copy all PDFs from multiple sources
  console.log('📄 Copying PDFs from all sources...');
  
  // Copy from public directory
  const publicCurrentDir = path.join('public', 'assets', 'current');
  const publicArchiveDir = path.join('public', 'assets', 'archive');
  
  if (fs.existsSync(publicCurrentDir)) {
    const files = fs.readdirSync(publicCurrentDir);
    files.forEach(file => {
      if (file.endsWith('.pdf')) {
        const sourcePath = path.join(publicCurrentDir, file);
        const destPath = path.join('dist', 'assets', 'current', file);
        fs.copyFileSync(sourcePath, destPath);
        console.log(`  ✅ Copied public current/${file}`);
      }
    });
  }

  if (fs.existsSync(publicArchiveDir)) {
    const files = fs.readdirSync(publicArchiveDir);
    files.forEach(file => {
      if (file.endsWith('.pdf')) {
        const sourcePath = path.join(publicArchiveDir, file);
        const destPath = path.join('dist', 'assets', 'archive', file);
        fs.copyFileSync(sourcePath, destPath);
        console.log(`  ✅ Copied public archive/${file}`);
      }
    });
  }

  // Copy from src/assets
  const srcCurrentDir = path.join('src', 'assets', 'current');
  const srcArchiveDir = path.join('src', 'assets', 'Archive Article');
  
  if (fs.existsSync(srcCurrentDir)) {
    const files = fs.readdirSync(srcCurrentDir);
    files.forEach(file => {
      if (file.endsWith('.pdf')) {
        const sourcePath = path.join(srcCurrentDir, file);
        const destPath = path.join('dist', 'assets', 'current', file);
        if (!fs.existsSync(destPath)) {
          fs.copyFileSync(sourcePath, destPath);
          console.log(`  ✅ Copied src current/${file}`);
        }
      }
    });
  }

  if (fs.existsSync(srcArchiveDir)) {
    const files = fs.readdirSync(srcArchiveDir);
    files.forEach(file => {
      if (file.endsWith('.pdf')) {
        const sourcePath = path.join(srcArchiveDir, file);
        const destPath = path.join('dist', 'assets', 'archive', file);
        if (!fs.existsSync(destPath)) {
          fs.copyFileSync(sourcePath, destPath);
          console.log(`  ✅ Copied src archive/${file}`);
        }
      }
    });
  }

  // Step 5: Fix any PDFs missing .pdf extension
  console.log('🔧 Fixing PDF extensions...');
  
  const fixPdfExtensions = (dir) => {
    if (!fs.existsSync(dir)) return;
    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      if (stats.isFile() && !file.endsWith('.pdf')) {
        // Check if it's a PDF by reading the file header
        const buffer = Buffer.alloc(4);
        const fd = fs.openSync(filePath, 'r');
        fs.readSync(fd, buffer, 0, 4, 0);
        fs.closeSync(fd);
        
        // PDF files start with %PDF
        if (buffer.toString('utf8', 0, 4) === '%PDF') {
          const newPath = filePath + '.pdf';
          fs.renameSync(filePath, newPath);
          console.log(`  ✅ Fixed extension: ${file} -> ${file}.pdf`);
        }
      }
    });
  };
  
  fixPdfExtensions('dist/assets/current');
  fixPdfExtensions('dist/assets/archive');
  
  // Step 6: Create fallback PDF access
  console.log('🔄 Creating fallback PDF access...');
  
  // Create direct PDF links in root assets directory
  const currentPdfs = fs.readdirSync('dist/assets/current').filter(f => f.endsWith('.pdf'));
  const archivePdfs = fs.readdirSync('dist/assets/archive').filter(f => f.endsWith('.pdf'));
  
  // Copy current PDFs to root assets for direct access
  currentPdfs.forEach(file => {
    const sourcePath = path.join('dist', 'assets', 'current', file);
    const destPath = path.join('dist', 'assets', file);
    if (!fs.existsSync(destPath)) {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`  ✅ Created fallback current/${file}`);
    }
  });
  
  // Copy archive PDFs to root assets for direct access
  archivePdfs.forEach(file => {
    const sourcePath = path.join('dist', 'assets', 'archive', file);
    const destPath = path.join('dist', 'assets', file);
    if (!fs.existsSync(destPath)) {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`  ✅ Created fallback archive/${file}`);
    }
  });

  // Step 7: Copy .htaccess
  console.log('⚙️ Copying .htaccess file...');
  const htaccessSource = path.join('public', '.htaccess');
  const htaccessDest = path.join('dist', '.htaccess');
  if (fs.existsSync(htaccessSource)) {
    fs.copyFileSync(htaccessSource, htaccessDest);
    console.log('  ✅ .htaccess copied');
  }

  // Step 8: Create _redirects file for Netlify
  console.log('🌐 Creating _redirects file...');
  const redirectsContent = `# Serve PDF files directly (prevent SPA routing)
/assets/current/*.pdf /assets/current/:splat 200!
/assets/archive/*.pdf /assets/archive/:splat 200!
/assets/*.pdf /assets/:splat 200!

# Serve all assets directly
/assets/* /assets/:splat 200

# SPA fallback for all other routes
/* /index.html 200
`;
  fs.writeFileSync(path.join('dist', '_redirects'), redirectsContent);
  console.log('  ✅ _redirects file created');

  // Step 9: Verify deployment
  console.log('🔍 Verifying deployment...');
  const indexPath = path.join('dist', 'index.html');
  if (!fs.existsSync(indexPath)) {
    throw new Error('index.html not found in dist directory');
  }

  const totalCurrentPdfs = fs.readdirSync('dist/assets/current').filter(f => f.endsWith('.pdf')).length;
  const totalArchivePdfs = fs.readdirSync('dist/assets/archive').filter(f => f.endsWith('.pdf')).length;
  const totalRootPdfs = fs.readdirSync('dist/assets').filter(f => f.endsWith('.pdf')).length;
  
  console.log(`  ✅ Found ${totalCurrentPdfs} current issue PDF files`);
  console.log(`  ✅ Found ${totalArchivePdfs} archive PDF files`);
  console.log(`  ✅ Found ${totalRootPdfs} total PDF files in assets root`);
  console.log(`  ✅ All PDFs accessible via multiple paths`);

  console.log('🎉 PDF deployment fix completed successfully!');
  console.log('📂 Files ready for deployment in ./dist directory');
  console.log('🔗 PDFs accessible via:');
  console.log('   - /assets/current/filename.pdf');
  console.log('   - /assets/archive/filename.pdf');
  console.log('   - /assets/filename.pdf (fallback)');
  
} catch (error) {
  console.error('❌ PDF deployment fix failed:', error.message);
  process.exit(1);
}
