<IfModule mod_rewrite.c>
  RewriteEngine On

  # Serve PDFs directly and bypass SPA fallback
  RewriteCond %{REQUEST_URI} \.(pdf)$ [NC]
  RewriteRule ^ - [L]

  # Serve assets directly
  RewriteCond %{REQUEST_URI} ^/assets/ [NC]
  RewriteRule ^ - [L]

  # SPA fallback: route all other requests to index.html
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteRule . /index.html [L]
</IfModule>

# Ensure correct MIME type for PDFs
AddType application/pdf .pdf

# Cache static assets aggressively
<FilesMatch "\.(js|css|png|jpg|jpeg|gif|svg|pdf)$">
  Header set Cache-Control "public, max-age=31536000, immutable"
</FilesMatch>

<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteBase /

  # Explicitly allow PDF files (with or without URL encoding)
  RewriteCond %{REQUEST_URI} \.pdf$ [NC]
  RewriteRule ^ - [L]

  # Serve existing files/directories directly (prevents rewriting PDFs to index.html)
  RewriteCond %{REQUEST_FILENAME} -f [OR]
  RewriteCond %{REQUEST_FILENAME} -d
  RewriteRule ^ - [L]

  # SPA fallback for other routes (but not for assets)
  RewriteCond %{REQUEST_URI} !^/assets/
  RewriteRule ^ index.html [L]
</IfModule>

# Ensure correct MIME for PDFs
AddType application/pdf .pdf

# Optional caching for assets
<FilesMatch "\.(pdf)$">
  ExpiresActive On
  ExpiresDefault "access plus 1 year"
  Header set Cache-Control "public, max-age=31536000, immutable"
</FilesMatch>

# Enable caching for static assets
<IfModule mod_expires.c>
  ExpiresActive On
  ExpiresByType image/jpg "access plus 1 year"
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType text/css "access plus 1 month"
  ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# MIME types
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType application/pdf .pdf
</IfModule>
