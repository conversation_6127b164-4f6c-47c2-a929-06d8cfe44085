/**
 * Normalize PDF filenames in public/assets/current:
 *  - replace spaces and commas with hyphens
 *  - remove problematic chars
 *  - update src/constants/currentIssueData.js to use new names
 */
const fs = require('fs');
const path = require('path');

const pdfDir = path.join(__dirname, '..', 'public', 'assets', 'current');
const constantsPath = path.join(__dirname, '..', 'src', 'constants', 'currentIssueData.js');

if (!fs.existsSync(pdfDir)) {
  console.error('PDF directory not found:', pdfDir);
  process.exit(1);
}

const pdfFiles = fs.readdirSync(pdfDir).filter(f => f.toLowerCase().endsWith('.pdf'));
if (pdfFiles.length === 0) {
  console.log('No PDFs found in', pdfDir);
  process.exit(0);
}

function safeName(name) {
  // keep extension, normalize base name
  const ext = path.extname(name);
  let base = path.basename(name, ext);
  base = base.replace(/[,\/\\]+/g, ' ');           // remove commas/slashes
  base = base.replace(/\s+/g, '-');                // spaces -> hyphen
  base = base.replace(/[^a-zA-Z0-9\-_.]/g, '');    // strip other chars
  base = base.replace(/\-+/g, '-');                // collapse multiple hyphens
  return base + ext;
}

const changes = [];
pdfFiles.forEach(file => {
  const newName = safeName(file);
  if (newName !== file) {
    const from = path.join(pdfDir, file);
    const to = path.join(pdfDir, newName);
    if (fs.existsSync(to)) {
      console.warn('Target exists, skipping:', to);
      return;
    }
    fs.renameSync(from, to);
    console.log(`Renamed: "${file}" -> "${newName}"`);
    changes.push({ old: file, new: newName });
  }
});

if (changes.length === 0) {
  console.log('No filenames needed normalization.');
  process.exit(0);
}

// Update currentIssueData.js replacements (safe string replacement on filenames)
let constants = fs.readFileSync(constantsPath, 'utf8');
changes.forEach(({ old, new: newName }) => {
  const oldEscaped = old.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
  const re = new RegExp(oldEscaped, 'g');
  constants = constants.replace(re, newName);
});
fs.writeFileSync(constantsPath, constants, 'utf8');
console.log('Updated', path.relative(process.cwd(), constantsPath), 'with new filenames.');