import React from 'react';
import { Box, Typography, Button, Paper, Avatar } from '@mui/material';
import { LinkedIn, Email } from '@mui/icons-material';

const EditorialMemberCard = ({ 
  member, 
  showTitle = false, 
  imagePosition = 'left',
  defaultImage = null 
}) => {
  const getImageSrc = () => {
    if (member.image) return member.image;
    if (defaultImage) return defaultImage;
    return `https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face`;
  };

  return (
    <Paper
      elevation={3}
      sx={{
        p: { xs: 3, sm: 4, md: 5 },
        mb: 4,
        borderRadius: 3,
        transition: 'all 0.3s ease-in-out',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 8px 25px rgba(234, 119, 23, 0.15)',
        },
        background: 'linear-gradient(135deg, #ffffff 0%, #fafafa 100%)',
        border: '1px solid #e5e5e5',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          alignItems: { xs: 'center', md: 'flex-start' },
          gap: { xs: 3, md: 4 },
          textAlign: { xs: 'center', md: 'left' },
        }}
      >
        {/* Image Section */}
        <Box
          sx={{
            flexShrink: 0,
            order: { xs: 1, md: imagePosition === 'left' ? 1 : 2 },
          }}
        >
          <Avatar
            src={getImageSrc()}
            alt={member.name}
            sx={{
              width: { xs: 120, sm: 140, md: 160 },
              height: { xs: 120, sm: 140, md: 160 },
              border: '4px solid #EA7717',
              boxShadow: '0 4px 12px rgba(234, 119, 23, 0.2)',
            }}
          />
        </Box>

        {/* Content Section */}
        <Box
          sx={{
            flex: 1,
            order: { xs: 2, md: imagePosition === 'left' ? 2 : 1 },
            width: '100%',
          }}
        >
          {/* Title (if provided) */}
          {showTitle && member.title && (
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: '#EA7717',
                mb: 2,
                fontSize: { xs: '1.2rem', sm: '1.4rem', md: '1.5rem' },
              }}
            >
              {member.title}
            </Typography>
          )}

          {/* Name */}
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              color: '#1e293b',
              mb: 1,
              fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2rem' },
              lineHeight: 1.2,
            }}
          >
            {member.name}
          </Typography>

          {/* Position */}
          {member.position && (
            <Typography
              variant="h6"
              sx={{
                fontWeight: 500,
                color: '#EA7717',
                mb: 1,
                fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' },
              }}
            >
              {member.position}
            </Typography>
          )}

          {/* Institution */}
          {member.institution && (
            <Typography
              variant="body1"
              sx={{
                color: '#64748b',
                mb: 2,
                fontSize: { xs: '0.9rem', sm: '1rem' },
                fontWeight: 500,
              }}
            >
              {member.institution}
            </Typography>
          )}

          {/* Designation (alternative to position) */}
          {member.designation && !member.position && (
            <Typography
              variant="body1"
              sx={{
                color: '#64748b',
                mb: 2,
                fontSize: { xs: '0.9rem', sm: '1rem' },
                fontWeight: 500,
              }}
            >
              {member.designation}
            </Typography>
          )}

          {/* Bio */}
          {member.bio && (
            <Typography
              variant="body1"
              sx={{
                color: '#475569',
                mb: 3,
                lineHeight: 1.8,
                fontSize: { xs: '0.95rem', sm: '1rem' },
                textAlign: 'justify',
                textJustify: 'inter-word',
                hyphens: 'auto',
              }}
            >
              {member.bio}
            </Typography>
          )}

          {/* Specialization */}
          {member.specialization && Array.isArray(member.specialization) && (
            <Box sx={{ mb: 3 }}>
              <Typography
                variant="subtitle2"
                sx={{
                  fontWeight: 600,
                  color: '#1e293b',
                  mb: 1,
                  fontSize: { xs: '0.85rem', sm: '0.9rem' },
                }}
              >
                Specialization:
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: '#64748b',
                  fontSize: { xs: '0.8rem', sm: '0.85rem' },
                  textAlign: 'justify',
                }}
              >
                {member.specialization.join(', ')}
              </Typography>
            </Box>
          )}

          {/* Contact Buttons */}
          <Box
            sx={{
              display: 'flex',
              gap: 2,
              flexWrap: 'wrap',
              justifyContent: { xs: 'center', md: 'flex-start' },
              mt: 2,
            }}
          >
            {member.linkedin && (
              <Button
                variant="outlined"
                startIcon={<LinkedIn />}
                href={member.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                sx={{
                  borderColor: '#EA7717',
                  color: '#EA7717',
                  fontSize: { xs: '0.8rem', sm: '0.85rem' },
                  px: { xs: 2, sm: 3 },
                  py: 1,
                  '&:hover': {
                    backgroundColor: '#EA7717',
                    color: 'white',
                    borderColor: '#EA7717',
                  },
                }}
              >
                LinkedIn
              </Button>
            )}
            {member.email && (
              <Button
                variant="outlined"
                startIcon={<Email />}
                href={`mailto:${member.email}`}
                sx={{
                  borderColor: '#64748b',
                  color: '#64748b',
                  fontSize: { xs: '0.8rem', sm: '0.85rem' },
                  px: { xs: 2, sm: 3 },
                  py: 1,
                  '&:hover': {
                    backgroundColor: '#64748b',
                    color: 'white',
                    borderColor: '#64748b',
                  },
                }}
              >
                Email
              </Button>
            )}
          </Box>
        </Box>
      </Box>
    </Paper>
  );
};

export default EditorialMemberCard;
