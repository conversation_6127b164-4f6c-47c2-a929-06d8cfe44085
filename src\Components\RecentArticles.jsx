// Recent Articles Component

function RecentArticles() {
  const articles = [
    {
      id: 1,
      title: "Constitutional Implications of Digital Privacy Laws",
      authors: "<PERSON>, <PERSON>, <PERSON>, <PERSON>",
      date: "October 15, 2024",
      abstract: "This paper examines the evolving landscape of digital privacy laws and their constitutional implications..."
    },
    {
      id: 2,
      title: "Comparative Analysis of International Arbitration Frameworks",
      authors: "<PERSON>, <PERSON>, <PERSON>, <PERSON>",
      date: "October 10, 2024",
      abstract: "A comprehensive study of international arbitration frameworks across different jurisdictions..."
    },
    {
      id: 3,
      title: "Legal Challenges in Artificial Intelligence Regulation",
      authors: "<PERSON>, <PERSON>, <PERSON>, P.",
      date: "October 5, 2024",
      abstract: "This research explores the emerging legal challenges in regulating artificial intelligence technologies..."
    }
  ];

  return (
    <div className="bg-gray-50 py-10">
      <div className="max-w-7xl mx-auto px-4">
        <h2 className="text-2xl font-bold text-center mb-8 text-indigo-800">Recent Articles</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {articles.map(article => (
            <div key={article.id} className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-bold mb-2 text-indigo-700">{article.title}</h3>
              <p className="text-sm text-gray-600 mb-2">{article.authors}</p>
              <p className="text-xs text-gray-500 mb-3">{article.date}</p>
              <p className="text-sm text-gray-700">{article.abstract}</p>
              <a href="#" className="inline-block mt-4 text-indigo-600 hover:text-indigo-800">Read more →</a>
            </div>
          ))}
        </div>
        <div className="text-center mt-8">
          <a href="#" className="inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-6 rounded">
            View All Articles
          </a>
        </div>
      </div>
    </div>
  );
}

export default RecentArticles;