import { useState, useEffect } from 'react';
import { getBreakpointValue } from '../utils';

/**
 * Custom hook for responsive design
 * @param {string} breakpoint - Breakpoint to watch (xs, sm, md, lg, xl)
 * @returns {boolean} True if screen size matches or exceeds breakpoint
 */
export const useResponsive = (breakpoint) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const checkMatch = () => {
      setMatches(window.innerWidth >= getBreakpointValue(breakpoint));
    };

    // Check on mount
    checkMatch();

    // Add event listener
    window.addEventListener('resize', checkMatch);

    // Cleanup
    return () => window.removeEventListener('resize', checkMatch);
  }, [breakpoint]);

  return matches;
};

/**
 * Hook to get current screen size category
 * @returns {string} Current breakpoint (xs, sm, md, lg, xl)
 */
export const useScreenSize = () => {
  const [screenSize, setScreenSize] = useState('md');

  useEffect(() => {
    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width < 600) setScreenSize('xs');
      else if (width < 900) setScreenSize('sm');
      else if (width < 1200) setScreenSize('md');
      else if (width < 1536) setScreenSize('lg');
      else setScreenSize('xl');
    };

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);

    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);

  return screenSize;
};
