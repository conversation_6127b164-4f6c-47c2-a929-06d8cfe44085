import React from 'react';
import { Container, Typography, Paper, Divider, Box, List, ListItem, ListItemText } from '@mui/material';

const AboutTheJournal = () => {
  return (
    <>
      {/* About the Journal Section */}
      <Container maxWidth="md" sx={{ py: 6 }}>
        <Paper
          elevation={3}
          sx={{
            p: 4,
            borderRadius: 3,
            backgroundColor: '#fafafa',
            lineHeight: 1.8,
          }}
        >
          {/* Heading */}
          <Typography
            variant="h4"
            sx={{
              mb: 2,
              fontWeight: 'bold',
              color: '#2c3e50',
              textAlign: 'center',
            }}
          >
            About the Journal
          </Typography>

          <Divider sx={{ mb: 3, bgcolor: '#1976d2', height: '2px' }} />

          {/* Content */}
          <Typography variant="body1" sx={{ mb: 2, textAlign: 'justify' }}>
            <strong>Lingaya’s <PERSON>ita <PERSON> Institute of Management and Science (LLDIMS)</strong>{' '}
            publishes the <em>Lingaya’s Lalita Devi Journal of Professional Studies (LLDJPS)</em> — a
            bi-annual, peer-reviewed, and refereed multidisciplinary journal.
          </Typography>

          <Typography variant="body1" sx={{ mb: 2, textAlign: 'justify' }}>
            LLDJPS aims to foster inquiry and provide a vibrant forum for scholarly discussion on
            contemporary issues across social, economic, legal, educational, and technological
            contexts.
          </Typography>

          <Typography variant="body1" sx={{ mb: 2, textAlign: 'justify' }}>
            The journal encourages fresh perspectives and innovative research that address evolving
            academic and professional challenges. It seeks to bring forward interdisciplinary studies
            and emerging methodologies, initiating dialogue across subject areas such as:
          </Typography>

          {/* Bulleted List */}
          <List dense>
            {['Management', 'Law', 'Education', 'Applied Sciences', 'Computer Applications', 'Humanities'].map((item, index) => (
              <ListItem key={index} sx={{ pl: 0 }}>
                <ListItemText primary={` ${item}`} />
              </ListItem>
            ))}
          </List>
        </Paper>
      </Container>

      {/* Focus of the Journal Section */}
      <Box sx={{ bgcolor: '#f9fafb', py: 8 }}>
        <Container maxWidth="md" sx={{ textAlign: 'center' }}>
          <Typography
            variant="h4"
            sx={{ fontWeight: 'bold', color: '#2c3e50', mb: 2 }}
          >
            Focus of the Journal
          </Typography>

          <Divider sx={{ mb: 4, bgcolor: '#1976d2', height: '2px', width: '80px', mx: 'auto' }} />

          <Typography
            variant="body1"
            sx={{ color: '#4a4a4a', lineHeight: 1.8, textAlign: 'justify' }}
          >
            The journal focuses on publishing high-quality research that addresses contemporary
            challenges and innovations across various domains. Its objective is to promote
            interdisciplinary collaboration and provide a platform for academic and professional
            discourse that influences policy and practice.
          </Typography>
        </Container>
      </Box>
    </>
  );
};

export default AboutTheJournal;
