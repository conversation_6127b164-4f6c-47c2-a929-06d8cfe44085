import React from 'react';
import { Typography, Box, Paper } from '@mui/material';

const PublisherInfo = () => {
  return (
    <Box sx={{ maxWidth: 900, mx: 'auto', p: 3 }}>
      <Typography variant="h3" component="h1" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 4, textAlign: 'center' }}>
        Publisher Information
      </Typography>
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Typography variant="body1" sx={{ mb: 2 }}>
          LINGAYA'S LALITA DEVI JOURNAL OF PROFESSIONAL STUDIES (LLDJPS) is published by Lingaya’s Lalita Devi Institute of Management & Sciences. The journal is committed to providing a platform for high-quality research in the field of professional studies.
        </Typography>
      </Paper>
      <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 2 }}>
        Indexing
      </Typography>
      <Paper elevation={2} sx={{ p: 4 }}>
        <Typography variant="body1">
          JCCC is recognized by several indexing and abstracting services. For more details on indexing, please refer to the official JCCC website.
        </Typography>
      </Paper>
    </Box>
  );
};

export default PublisherInfo;
