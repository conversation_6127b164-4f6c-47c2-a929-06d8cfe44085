import React from 'react';
import { Typography, Box, Paper, Grid, TextField, Button, Alert } from '@mui/material';
import { Email, Phone, WhatsApp, Language, Description } from '@mui/icons-material';

const ContactUs = () => {
  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: { xs: 3, sm: 4, md: 6 } }}>
      <Typography
        variant="h2"
        component="h1"
        sx={{
          fontWeight: 700,
          color: '#EA7717',
          mb: { xs: 2, sm: 3 },
          textAlign: 'center',
          fontSize: { xs: '1.75rem', sm: '2.25rem', md: '2.75rem', lg: '3rem' },
          letterSpacing: '-0.025em',
          px: { xs: 1, sm: 2 }
        }}
      >
        Contact Us
      </Typography>

      <Typography
        variant="h5"
        sx={{
          color: 'text.secondary',
          mb: 6,
          textAlign: 'center',
          fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.5rem' },
          px: { xs: 2, sm: 4 },
          lineHeight: 1.6,
          fontWeight: 400,
          maxWidth: '800px',
          mx: 'auto'
        }}
      >
        For all communication regarding how to publish a paper, check the status of paper, or any other general information
      </Typography>

  
      {/* Support Information */}
      <Alert severity="info" sx={{ mb: 4 }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
          24/7 Support Available
        </Typography>
        <Typography variant="body2">
          We provide round-the-clock support for authors and researchers. You can reach us via email, phone, or WhatsApp for immediate assistance with manuscript submission, tracking, or any other queries.
        </Typography>
      </Alert>

      {/* Quick Links */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3 }}>
          Quick Links
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <a href="/submit-article" style={{ color: '#EA7717', textDecoration: 'none' }}>Submit Article</a>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <a href="/repository/current" style={{ color: '#EA7717', textDecoration: 'none' }}>Current Issue</a>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <a href="/aboutus/instructions" style={{ color: '#EA7717', textDecoration: 'none' }}>Author Guidelines</a>
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body1" sx={{ mb: 1 }}>
              <a href="/policies/publication-policies" style={{ color: '#EA7717', textDecoration: 'none' }}>Publication Policy</a>
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* Contact Form */}
      <Paper elevation={2} sx={{ p: 4 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3 }}>
          Send us a Message
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Full Name"
              variant="outlined"
              required
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Email"
              type="email"
              variant="outlined"
              required
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Subject"
              variant="outlined"
              required
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Message"
              multiline
              rows={4}
              variant="outlined"
              required
            />
          </Grid>
          <Grid item xs={12}>
            <Button
              variant="contained"
              size="large"
              sx={{
                bgcolor: '#EA7717',
                '&:hover': { bgcolor: '#b91c1c' }
              }}
            >
              Send Message
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default ContactUs;
