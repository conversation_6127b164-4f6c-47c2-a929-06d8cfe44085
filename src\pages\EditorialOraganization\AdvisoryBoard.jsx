import React from 'react';
import { Container, Typography, Box, ThemeProvider } from '@mui/material';
import Navbar from '../../Components/Navbar';
import Footer from '../../Components/Footer';
import EditorialMemberCard from '../../Components/EditorialMemberCard';
import theme from '../../theme/responsiveTheme';

// Import images for advisory board members
import advisorImage1 from '../../assets/Images/WhatsApp Image 2025-08-27 at 00.56.02_dcc55908.jpg';
import advisorImage2 from '../../assets/Images/WhatsApp Image 2025-08-27 at 00.56.08_676d526f.jpg';
import advisorImage3 from '../../assets/Images/WhatsApp Image 2025-08-27 at 00.56.16_f5454d91.jpg';
import advisorImage4 from '../../assets/Images/WhatsApp Image 2025-08-27 at 00.56.25_8d270503.jpg';
import advisorImage5 from '../../assets/Images/WhatsApp Image 2025-08-27 at 00.57.10_4e43c314.jpg';
import advisorImage6 from '../../assets/Images/WhatsApp Image 2025-08-27 at 00.57.19_b2641543.jpg';

const AdvisoryBoard = () => {
  const advisoryMembers = [
    {
      name: 'Dr. Jyoti Dahiya',
      title: 'Associate Editor',
      position: 'Head of Department',
      institution: 'Department of Education, LLDIMS, Delhi, India',
      bio: 'Advanced credentials including Psychology, Political Science, Guidance & Counselling, with multidisciplinary expertise in educational research and development.',
      image: advisorImage1,
      specialization: ['Psychology', 'Political Science', 'Guidance & Counselling'],
    },
    {
      name: 'Dr. Slim Javed',
      title: 'Associate Editor',
      position: 'Head of Department',
      institution: 'Department of Journalism, LLDIMS, Delhi, India',
      bio: 'An accomplished scholar in film studies, with pedagogical expertise in concept-to-production learning, inquiry-driven methods, flipped classrooms, and collaborative projects.',
      image: advisorImage2,
      specialization: ['Film Studies', 'Journalism', 'Media Production'],
    },
    {
      name: 'Rajesh R. Mishra',
      title: 'Executive Editor',
      position: 'Executive Editor',
      institution: 'LLDIMS, Delhi, India',
      bio: 'Expertise in Agile methodology, Research Analyst, Data science. Responsible for Day-to-day affairs of the Journal with extensive experience in academic publishing.',
      image: advisorImage3,
      specialization: ['Agile Methodology', 'Research Analysis', 'Data Science'],
    },
    {
      name: 'Prof. (Dr.) Jay Prakash Yadav',
      title: 'Editorial Board Member',
      position: 'Director',
      institution: 'Chandigarh Group of Colleges Jhanjeri, Punjab, India',
      bio: 'Specializing in Constitutional Law and Parliamentary Studies, with expertise in legal frameworks and legislative processes.',
      image: advisorImage4,
      linkedin: 'https://linkedin.com/in/prof-dr-jay-prakash-yadav-89080533',
      specialization: ['Constitutional Law', 'Parliamentary Studies', 'Legal Frameworks'],
    },
    {
      name: 'Prof. (Dr.) Priya A Sondhi',
      title: 'Editorial Board Member',
      position: 'Dean, School of Law',
      institution: 'Sushant University, Gurugram, India',
      bio: 'Expert in International Law, Global Legal Systems, and Disaster Management Legislation with extensive research in legal frameworks.',
      image: advisorImage5,
      linkedin: 'https://linkedin.com/in/dr-priya-a-sondhi-640552161',
      email: '<EMAIL>',
      specialization: ['International Law', 'Global Legal Systems', 'Disaster Management Legislation'],
    },
    {
      name: 'Prof. (Dr.) Rajanikant Verma',
      title: 'Editorial Board Member',
      position: '',
      institution: 'Zakir Husain Delhi College, University of Delhi, India',
      bio: 'Secretary of Indian Commerce Association Delhi-NCR Chapter. Specializes in Participative Management, Cyber Laws, Business Laws and Industrial Laws.',
      image: advisorImage6,
      linkedin: 'https://linkedin.com/in/prof-rajanikant-verma-b7253652',
      specialization: ['Participative Management', 'Cyber Laws', 'Business Laws', 'Industrial Laws'],
    },
  ];

  return (
    <>
      <Navbar />
      <ThemeProvider theme={theme}>
        <Box sx={{ minHeight: '100vh', bgcolor: 'background.default', py: { xs: 4, md: 8 } }}>
          <Container maxWidth="lg">
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: 6 }}>
              <Typography
                variant="h2"
                component="h1"
                sx={{
                  fontWeight: 700,
                  color: '#EA7717',
                  mb: 2,
                  fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' }
                }}
              >
                Advisory Board
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: 'text.secondary',
                  maxWidth: 600,
                  mx: 'auto',
                  fontSize: { xs: '1rem', sm: '1.1rem' }
                }}
              >
                Distinguished advisors and editorial team members
              </Typography>
            </Box>

            {/* Advisory Board Members */}
            <Box sx={{ maxWidth: 900, mx: 'auto' }}>
              {advisoryMembers.map((member, index) => (
                <EditorialMemberCard
                  key={index}
                  member={member}
                  showTitle={true}
                  imagePosition="left"
                />
              ))}
            </Box>
          </Container>
        </Box>
      </ThemeProvider>
      <Footer />
    </>
  );
};

export default AdvisoryBoard;
