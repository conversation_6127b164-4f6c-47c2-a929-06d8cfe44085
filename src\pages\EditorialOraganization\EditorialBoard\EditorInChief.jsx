import React from 'react';
import { Container, Typography, Box, ThemeProvider } from '@mui/material';
import Navbar from '../../../Components/Navbar';
import Footer from '../../../Components/Footer';
import EditorialMemberCard from '../../../Components/EditorialMemberCard';
import theme from '../../../theme/responsiveTheme';
import editorImage from '../../../assets/Images/WhatsApp Image 2025-08-25 at 15.49.06_aa5aeba7.jpg';

const EditorInChief = () => {
  const editorData = [
    {
      title: "Editor-in-Chief",
      name: "Prof. (Dr.) <PERSON><PERSON><PERSON>",
      position: "Director, Lingaya's Lalita Devi Institute of Management & Sciences, Delhi",
      institution: "",
      linkedin: "https://linkedin.com/in/dr-pranav-mishra-82a51427",
      image: editorImage,
      bio: "Prof. (Dr.) <PERSON><PERSON><PERSON> serves as the Director of Lingaya's <PERSON>ita Devi Institute of Management & Sciences, Delhi, bringing extensive experience in academic leadership and management education. He has been instrumental in advancing the institution's research initiatives and fostering academic excellence.",
    },
  ];

  return (
    <>
      <ThemeProvider theme={theme}>
        <Box sx={{ minHeight: '100vh', bgcolor: 'background.default', py: { xs: 4, md: 8 } }}>
          <Container maxWidth="lg">
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: 6 }}>
              <Typography
                variant="h2"
                component="h1"
                sx={{
                  fontWeight: 700,
                  color: '#EA7717',
                  mb: 2,
                  fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' }
                }}
              >
                Editor-in-Chief
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: 'text.secondary',
                  maxWidth: 600,
                  mx: 'auto',
                  fontSize: { xs: '1rem', sm: '1.1rem' }
                }}
              >
                Leading our editorial vision and academic excellence
              </Typography>
            </Box>

            {/* Editor Cards */}
            <Box sx={{ maxWidth: 900, mx: 'auto' }}>
              {editorData.map((editor, index) => (
                <EditorialMemberCard
                  key={index}
                  member={editor}
                  showTitle={true}
                  imagePosition="left"
                />
              ))}
            </Box>
          </Container>
        </Box>
      </ThemeProvider>
    </>
  );
};

export default EditorInChief;
