import React from 'react';
import { Container, Typography, Box, ThemeProvider } from '@mui/material';
import Footer from '../../../Components/Footer';
import EditorialMemberCard from '../../../Components/EditorialMemberCard';
import theme from '../../../theme/responsiveTheme';

// Import images
import image1 from '../../../assets/Images/WhatsApp Image 2025-08-26 at 13.23.24_ca94726b.jpg';
import image2 from '../../../assets/Images/WhatsApp Image 2025-08-26 at 13.23.31_dd0840fa.jpg';
import image3 from '../../../assets/Images/WhatsApp Image 2025-08-27 at 00.55.32_a6648991.jpg';
import image4 from '../../../assets/Images/WhatsApp Image 2025-08-27 at 00.55.40_09b71db0.jpg';
import image5 from '../../../assets/Images/WhatsApp Image 2025-08-27 at 00.55.48_ece76763.jpg';
import image6 from '../../../assets/Images/WhatsApp Image 2025-08-27 at 00.55.54_681afbfb.jpg';
import image7 from '../../../assets/Images/WhatsApp Image 2025-08-27 at 00.56.02_dcc55908.jpg';
import image8 from '../../../assets/Images/WhatsApp Image 2025-08-27 at 00.56.08_676d526f.jpg';
import image9 from '../../../assets/Images/WhatsApp Image 2025-08-27 at 00.56.16_f5454d91.jpg';
import image10 from '../../../assets/Images/WhatsApp Image 2025-08-27 at 00.56.25_8d270503.jpg';
import image11 from '../../../assets/Images/WhatsApp Image 2025-08-27 at 00.56.34_b6ceb71e.jpg';
import image12 from '../../../assets/Images/rakesh.jpg';
import image13 from '../../../assets/Images/Prof. (Dr) Md.  Iqbal.jpg'; // Image not available

const editorialData = {
  editorialBoard: [
    {
      name: 'Prof. (Dr.) Durgesh Tripathi',
      position: 'Dean, University School of Mass Communication, GGSIPU, India',
      institution: '',
      bio: 'Co-developer of (e-PG Pathshala), and National Coordinator for SWAYAM MOOCs. He has chaired sessions across China, Thailand, South Korea, Malaysia, Germany, Turkey, the UK, USA, and Australia, and has participated in EU-funded projects and UN forums.',
      linkedin: 'https://linkedin.com/in/prof-dr-durgesh-tripathi-06536a3b',
      email: '<EMAIL>',
      image: image1,
    },
    {
      name: 'Dr. Sachin Kumar Mangla',
      position: 'Plymouth Business School, University of Plymouth, UK',
      institution: '',
      bio: 'Strategy Planner and Advisor to Higher Management Institutions with extensive experience in organizational development.',
      linkedin: 'https://linkedin.com/in/sachin-mangla-kumar-42aa1568',
      email: '<EMAIL>',
      image: image2,
    },
    {
      name: 'Prof. (Dr.) Hamendra Kumar Dangi',
      position: 'Professor, Delhi School of Economics, DU, India',
      institution: ' ',
      bio: 'Expert in Business Analytics, Business Research Methods and Data Science with extensive research publications.',
      linkedin: 'https://linkedin.com/in/h-k-dangi-*********',
      email: '<EMAIL>',
      image: image3,
    },
    {
      name: 'Prof. Aalok Kumar',
      position: 'Department of Production & Operations Management, IIM Vishakhapatnam, India',
      institution: '',
      bio: 'Skilled in Supply Chain Optimization, Analytical Skills, Operations and Production Management with strong research background.',
      linkedin: 'https://linkedin.com/in/aalok-kumar-phd-15a50855',
      email: '<EMAIL>',
      image: image4,
    },
    {
      name: 'Prof. (Dr.) Sanjiv Mittal',
      position: 'Professor, University School of Management Studies, GGSIPU, India ',
      institution: '',
      bio: 'Specializing in Marketing, Finance, Foreign Trade Policy, International Financial Management, Strategic Management, and Entrepreneurship.',
      linkedin: 'https://linkedin.com/in/dr-sanjiv-mittal-4235851b1',
      email: '<EMAIL>',
      image: image5,
    },
    {
      name: 'Dr. Meghna Goel',
      position: 'Department of HRM, K J Somaiya Institute of Management, Mumbai, India',
      institution: '',
      bio: 'Expert in Organizational behavior, Performance management, Entrepreneurial Leadership & Behavior, and Competitive Decision-Making.',
      linkedin: 'https://linkedin.com/in/meghna-goel-70b950237',
      email: '<EMAIL>',
      image: image6,
    },
    {
      name: 'Dr. Jay Prakash Yadav',
      position: 'Director , Chandigarh Group of Colleges Jhanjeri, Punjab, India',
      institution: '',
      bio: 'Specializing in Constitutional Law and Parliamentary Studies, with expertise in legal frameworks and legislative processes.',
      linkedin: 'https://linkedin.com/in/prof-dr-jay-prakash-yadav-89080533',
      email: '<EMAIL>',
      image: image7,
    },
    {
      name: 'Prof. (Dr) Priya Sondhi',
      position: 'Dean, School of Law,Sushant University, Gurugram, India',
      institution: '',
      bio: 'Expert in International Law, Global Legal Systems, and Disaster Management Legislation.',
      linkedin: 'https://linkedin.com/in/dr-priya-a-sondhi-640552161',
      email: '',
      image: image8,
    },
    {
      name: 'Dr. Rajanikant Verma',
      position: 'Associate Professor, Zakir Husain Delhi College, University of Delhi, India',
      institution: '',
      bio: 'Specializing in Organizational Behavior, Human Resource Management, Financial and Investment Management, Corporate, Business and Industrial Laws.',
      linkedin: 'https://linkedin.com/in/prof-rajanikant-verma-b7253652',
      email: '<EMAIL>',
      image: image9,
    },
    {
      name: 'Dr. Amit Ahuja',
      position: 'Associate Professor, University School of Education, GGSIPU Delhi, India',
      institution: '',
      bio: 'Specialization in Chemistry, Educational Technology, Educational Psychology, Science Education, Environmental Education, Educational & Vocational Guidance and Social & Adult Education.',
      linkedin: 'https://linkedin.com/in/dr-amit-ahuja-15ab4031b',
      email: '<EMAIL>',
      image: image10,
    },
    {
      name: 'Dr. Rakesh Kumar Gupta',
      position: 'School of Management, Dr. B. R. Ambedkar University, Delhi, India',
      institution: '',
      bio: 'An academician with expertise in Entrepreneurship, Accounting, Finance, and Business Law, has served in universities and government institutions, including SCERT Delhi, University of Delhi, and GGSIP University.',
      linkedin: 'https://linkedin.com/in/dr-rk-gupta',
      email: '<EMAIL>',
      image: image12,
    },
    {
      name: 'Dr. Salini Yadav',
      position: 'University School of Education, GGSIPU Delhi, India',
      institution: '',
      bio: 'Expert in Philosophical Foundations of Education, Human Rights Education, Educational Planning and Management, Transformational Leadership, Scientific Literacy.',
      linkedin: 'https://linkedin.com/in/dr-shalini-yadava-13a01914',
      email: '<EMAIL>',
      image: image11,
     
    },    {
      name: 'Prof. (Dr) Md.  Iqbal',
      position: 'Department of Computer Science, Quantum University, Roorkee, Uttarakhand, India',
      institution: '',
      bio: ' In the Department of Computer Science & Engineering he is specializes in Artificial Intelligence, Cloud Computing, Machine Learning, and Blockchain Technology, fostering innovation, research, and advanced technological solutions..',
      linkedin: 'https://linkedin.com/in/dr-md-iqbal-57319b6b',
      email: '<EMAIL>',
      image:image13 ,
     
    }
  ],
};

const EditorialBoardMembers = () => {
  return (
    <>
      <ThemeProvider theme={theme}>
        <Box sx={{ minHeight: '100vh', bgcolor: 'background.default', py: { xs: 4, md: 8 } }}>
          <Container maxWidth="lg">
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: 6 }}>
              <Typography
                variant="h2"
                component="h1"
                sx={{
                  fontWeight: 700,
                  color: '#EA7717',
                  mb: 2,
                  fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' }
                }}
              >
                Editorial Board Members
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: 'text.secondary',
                  maxWidth: 600,
                  mx: 'auto',
                  fontSize: { xs: '1rem', sm: '1.1rem' }
                }}
              >
                Distinguished scholars and professionals guiding our academic excellence
              </Typography>
            </Box>

            {/* Editorial Board Members */}
            <Box sx={{ maxWidth: 900, mx: 'auto' }}>
              {editorialData.editorialBoard.map((member, index) => (
                <EditorialMemberCard
                  key={index}
                  member={member}
                  showTitle={false}
                  imagePosition="left"
                />
              ))}
            </Box>
          </Container>
        </Box>
      </ThemeProvider>
    </>
  );
};

export default EditorialBoardMembers;
