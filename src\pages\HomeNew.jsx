import React, { useEffect, useState } from 'react';
import { Typo<PERSON>, Box, Button, Paper, Alert } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';

// Sample Notifications
const notifications = [
  "Call for Papers: Submit your research for the next issue!",
  "Latest Issue Released: Explore the Current Volume.",
  "Special Edition Coming Soon: Stay tuned for updates!"
];

function HomeNew() {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Auto-slide notifications every 4 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % notifications.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  return (
    <>

      {/* Hero Banner */}
      <Box
        sx={{
          background: 'linear-gradient(to right, #EA7717, #ff8a4c)',
          color: 'white',
          p: { xs: 2, sm: 3, md: 4 },
          borderRadius: 1,
          mb: 4,
          textAlign: 'center'
        }}
      >
        <Typography
          variant="h2"
          component="h1"
          sx={{
            fontWeight: 700,
            mb: 3,
            fontSize: { xs: '1.75rem', sm: '2.25rem', md: '3rem', lg: '3.5rem' },
            lineHeight: { xs: 1.2, sm: 1.15, md: 1.1 },
            letterSpacing: '-0.025em',
            textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            px: { xs: 1, sm: 2 }
          }}
        >
          Lingaya's Lalita Devi Journal of Professional Studies
        </Typography>
        <Typography
          variant="h5"
          sx={{
            mb: 3,
            fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.5rem' },
            fontWeight: 500,
            opacity: 0.95,
            letterSpacing: '0.05em'
          }}
        >
          LLDJPS - A Bi-Annual Peer-Reviewed Journal
        </Typography>
        <Typography
          variant="body1"
          sx={{
            mb: 3,
            maxWidth: '1000px',
            mx: 'auto',
            fontSize: { xs: '0.95rem', sm: '1.05rem' },
            px: { xs: 2, sm: 3 },
            fontWeight: 500,
            lineHeight: 1.7,
            textAlign: 'justify',
            textJustify: 'inter-word',
            hyphens: 'auto'
          }}
        >
          Lingaya’s Lalita Devi Journal of Professional Studies (LLDJPS) is a bi-annual, peer-reviewed journal offering a platform for scholarly dialogue on contemporary issues across social, economic, legal, educational, and technological domains. The journal promotes innovative, interdisciplinary research and critical inquiry in theory, practice, and policy-oriented academic work.
        </Typography>

        <Box
          sx={{
            display: 'flex',
            gap: { xs: 1, sm: 2 },
            justifyContent: 'center',
            flexWrap: 'wrap',
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: 'center'
          }}
        >
          <Button
            variant="contained"
            size="large"
            component={RouterLink}
            to="/submit-article"
            sx={{
              bgcolor: 'white',
              color: '#EA7717',
              '&:hover': { bgcolor: '#f5f5f5' },
              width: { xs: '100%', sm: 'auto' },
              maxWidth: { xs: '300px', sm: 'none' }
            }}
          >
            Submit Article
          </Button>
          <Button
            variant="outlined"
            size="large"
            component={RouterLink}
            to="/repository/current"
            sx={{
              borderColor: 'white',
              color: 'white',
              '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' },
              width: { xs: '100%', sm: 'auto' },
              maxWidth: { xs: '300px', sm: 'none' }
            }}
          >
            Current Issue
          </Button>
        </Box>
      </Box>

      {/* Footer Note */}
      <Box sx={{ textAlign: 'center', mt: 6, color: '#888' }}>
        <Typography variant="body2">
          © 2025 COPYRIGHT, LLDJPS(ISSN 2230-987X). ALL RIGHTS RESERVED.
        </Typography>
      </Box>
    </>
  );
}

export default HomeNew;
