import React from 'react';
import { Typography, Box, Paper, Grid, List, ListItem, ListItemIcon, ListItemText, Alert, Button, Chip } from '@mui/material';
import { CheckCircle, Book, Description, Email, Phone, WhatsApp, Download, CloudUpload } from '@mui/icons-material';

const BookPublished = () => {
  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h3" component="h1" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 4, textAlign: 'center' }}>
        Publish Book
      </Typography>

      {/* Introduction */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3 }}>
          LINGAYA'S LALITA DEVI JOURNAL OF PROFESSIONAL STUDIES (LLDJPS) - Book Publishing
        </Typography>
        <Typography variant="body1" sx={{ color: 'text.secondary', mb: 3 }}>
          <strong>ISSN:</strong> 2230-987X | <strong>Impact Factor:</strong> 4.8 | <strong>IC Value:</strong> 94.33
        </Typography>
        <Typography variant="body1" sx={{ color: 'text.secondary' }}>
          LLDJPS offers comprehensive book publishing services for authors, researchers, and academicians. 
          We provide professional publishing solutions with global distribution and academic recognition.
        </Typography>
      </Paper>

      {/* Why Publish with IJAR */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3 }}>
          Why Publish Your Book with IJAR?
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <List>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Global Distribution"
                  secondary="Worldwide availability through major book distributors"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Academic Recognition"
                  secondary="Published books are indexed in major academic databases"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Professional Editing"
                  secondary="Comprehensive editing and proofreading services"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="ISBN Assignment"
                  secondary="International Standard Book Number for global identification"
                />
              </ListItem>
            </List>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <List>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Digital Publishing"
                  secondary="E-book formats for modern reading platforms"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Marketing Support"
                  secondary="Promotional activities and author marketing"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Royalty Sharing"
                  secondary="Competitive royalty rates for authors"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="24/7 Support"
                  secondary="Round-the-clock assistance for authors"
                />
              </ListItem>
            </List>
          </Grid>
        </Grid>
      </Paper>

      {/* Book Categories */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3 }}>
          Book Categories We Publish
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={4}>
            <Paper elevation={1} sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <Book sx={{ color: '#EA7717', fontSize: 40, mb: 2 }} />
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                Academic Books
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Research monographs, textbooks, reference books, and scholarly publications
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Paper elevation={1} sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <Book sx={{ color: '#EA7717', fontSize: 40, mb: 2 }} />
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                Conference Proceedings
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Collections of papers from academic conferences and symposiums
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Paper elevation={1} sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <Book sx={{ color: '#EA7717', fontSize: 40, mb: 2 }} />
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                Edited Volumes
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Multi-author books with contributions from various experts
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Paper elevation={1} sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <Book sx={{ color: '#EA7717', fontSize: 40, mb: 2 }} />
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                Research Reports
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Comprehensive research findings and technical reports
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Paper elevation={1} sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <Book sx={{ color: '#EA7717', fontSize: 40, mb: 2 }} />
             
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Paper elevation={1} sx={{ p: 3, textAlign: 'center', height: '100%' }}>
              <Book sx={{ color: '#EA7717', fontSize: 40, mb: 2 }} />
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                Professional Books
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Industry-specific books and professional guides
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Paper>

      {/* Publishing Process */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3 }}>
          Book Publishing Process
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, color: '#EA7717' }}>
              Step 1: Manuscript Submission
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <CloudUpload sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Submit Manuscript"
                  secondary="Complete book manuscript in Word format"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Description sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Author Information"
                  secondary="Complete author details and bio"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Description sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Cover Design Ideas"
                  secondary="Suggestions for book cover design"
                />
              </ListItem>
            </List>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, color: '#EA7717' }}>
              Step 2: Review & Editing
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Initial Review"
                  secondary="Content evaluation and market assessment"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Professional Editing"
                  secondary="Comprehensive editing and proofreading"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Author Approval"
                  secondary="Final review and approval by author"
                />
              </ListItem>
            </List>
          </Grid>
        </Grid>
      </Paper>

      {/* Publishing Timeline */}
  

      {/* Downloads */}
  

      {/* Important Notes */}
      <Alert severity="warning" sx={{ mb: 4 }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
          Important Notes for Authors
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          • Ensure your manuscript is original and has not been published elsewhere
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          • Manuscripts should be well-structured and professionally written
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          • Include all necessary permissions for copyrighted materials
        </Typography>
        <Typography variant="body2">
          • Follow the journal's formatting guidelines for book manuscripts
        </Typography>
      </Alert>

      {/* Contact Information */}
      <Paper elevation={2} sx={{ p: 4, bgcolor: '#fff8f1', border: '1px solid #EA7717' }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3, textAlign: 'center' }}>
          Ready to Publish Your Book?
        </Typography>
        <Typography variant="body1" sx={{ textAlign: 'center', mb: 3 }}>
          Contact our book publishing team for personalized assistance and guidance
        </Typography>
        <Grid container spacing={3} sx={{ textAlign: 'center' }}>
          <Grid item xs={12} sm={4}>
            <Email sx={{ color: '#EA7717', fontSize: 40, mb: 1 }} />
            <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
              Email
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              
            </Typography>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Phone sx={{ color: '#EA7717', fontSize: 40, mb: 1 }} />
            <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
              Phone
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              
            </Typography>
          </Grid>
          <Grid item xs={12} sm={4}>
            <WhatsApp sx={{ color: '#EA7717', fontSize: 40, mb: 1 }} />
            <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
              WhatsApp
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              
            </Typography>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default BookPublished;
