import React from 'react'; import Footer from '../../Components/Footer';
import { Container, Typography, Paper, List, ListItem, ListItemIcon, ListItemText } from '@mui/material'; import { Check as CheckIcon } from '@mui/icons-material';
function CancellationRefund() {
    return (
        <>           
            <div className="min-h-screen bg-orange-50">                <Container maxWidth="lg" sx={{ py: 8 }}>
                <Typography variant="h2"
                    component="h1" align="center"
                    gutterBottom sx={{
                        color: 'primary.main', fontWeight: 'bold',
                        mb: 6,
                    }}
                >                        Cancellation & Refund Policy
                </Typography>                    <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
                    <Typography variant="h5" gutterBottom color="primary" fontWeight="bold">                            Article Processing Charges (APC) Refund Policy
                    </Typography>                        <Typography paragraph>
                        The Journal of Legal Studies (JLS) has established clear guidelines regarding the cancellation and refund of Article Processing Charges (APC).                        </Typography>
                    <List>                            <ListItem>
                        <ListItemIcon>                                    <CheckIcon color="primary" />
                        </ListItemIcon>                                <ListItemText
                            primary="Pre-Review Withdrawal" secondary="Full refund if manuscript is withdrawn before peer review process begins"
                        />                            </ListItem>
                        <ListItem>                                <ListItemIcon>
                            <CheckIcon color="primary" />                                </ListItemIcon>
                            <ListItemText
                                primary="During Review Process" secondary="50% refund if withdrawn during the peer review process" />
                        </ListItem>                            <ListItem>
                            <ListItemIcon>                                    <CheckIcon color="primary" />
                            </ListItemIcon>                                <ListItemText
                                primary="Post-Review Rejection" secondary="75% refund if manuscript is rejected after peer review"
                            />                            </ListItem>
                        <ListItem>                                <ListItemIcon>
                            <CheckIcon color="primary" />                                </ListItemIcon>
                            <ListItemText primary="Post-Acceptance"
                                secondary="No refund after manuscript acceptance and publication" />
                        </ListItem>                        </List>
                </Paper>                </Container>
            </div>            <Footer />
        </>
    );
}

export default CancellationRefund;
