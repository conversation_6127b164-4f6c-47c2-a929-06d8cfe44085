import React from 'react'; import Footer from '../../Components/Footer';
import { Container, Typography, Paper, Box, List, ListItem, ListItemIcon, ListItemText } from '@mui/material'; import { Check as CheckIcon, LockOpen as LockOpenIcon } from '@mui/icons-material';
function Licensing() {
    return (
        <>      <Navbar />
            <div className="min-h-screen bg-orange-50">        <Container maxWidth="lg" sx={{ py: 8 }}>
                <Typography variant="h2"
                    component="h1"
                    align="center" gutterBottom sx={{
                        color: 'primary.main',
                        fontWeight: 'bold', mb: 6,
                    }}
                >
                    Licensing Policy          </Typography>
                <Paper elevation={3} sx={{ p: 4, mb: 4 }}>            <Typography variant="h5" gutterBottom color="primary" fontWeight="bold">
                    Open Access Licensing            </Typography>
                    <Typography paragraph>
                        The Journal of Legal Studies (JLS) publishes all articles under the Creative Commons Attribution 4.0 International License (CC BY 4.0). This license ensures maximum visibility and accessibility while protecting authors' rights.            </Typography>
                    <List>              <ListItem>
                        <ListItemIcon>                  <CheckIcon color="primary" />
                        </ListItemIcon>                <ListItemText
                            primary="Authors retain copyright" secondary="While publishing with JLS, authors maintain full copyright of their work"
                        />              </ListItem>
                        <ListItem>                <ListItemIcon>
                            <CheckIcon color="primary" />                </ListItemIcon>
                            <ListItemText primary="Free to share and adapt"
                                secondary="Users can share, copy, redistribute, and adapt the material for any purpose, even commercially" />
                        </ListItem>              <ListItem>
                            <ListItemIcon>                  <CheckIcon color="primary" />
                            </ListItemIcon>                <ListItemText
                                primary="Attribution required" secondary="Appropriate credit must be given to authors and any changes made must be indicated"
                            />              </ListItem>
                    </List>          </Paper>
                <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
                    <Typography variant="h5" gutterBottom color="primary" fontWeight="bold">              Terms of Use
                    </Typography>            <Typography paragraph>
                        Users of JLS content must adhere to the following terms:            </Typography>
                    <List>              <ListItem>
                        <ListItemIcon>                  <CheckIcon color="primary" />
                        </ListItemIcon>                <ListItemText
                            primary="Proper Citation" secondary="All uses must properly cite the original publication in JLS"
                        />              </ListItem>
                        <ListItem>                <ListItemIcon>
                            <CheckIcon color="primary" />                </ListItemIcon>
                            <ListItemText primary="No Misrepresentation"
                                secondary="The original meaning of the content must not be distorted" />
                        </ListItem>              <ListItem>
                            <ListItemIcon>                  <CheckIcon color="primary" />
                            </ListItemIcon>                <ListItemText
                                primary="Link to Original" secondary="When possible, include a link to the original article on the JLS website"
                            />              </ListItem>
                    </List>          </Paper>
            </Container>
            </div>      <Footer />
        </>
    )
}

export default Licensing;
;
