import React from 'react';
import Footer from '../../Components/Footer';
import Navbar from '../../Components/Navbar';
import {
  Container,
  Typography,
  Paper,
  Grid,
  ThemeProvider,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import theme from '../../theme/responsiveTheme';

function OpenAccess() {
  return (
    <>
      <div className="min-h-screen bg-orange-50">
        <ThemeProvider theme={theme}>
          <Container maxWidth="lg" sx={{ py: 8 }}>
            {/* Page Title */}
            <Typography
              variant="h2"
              component="h1"
              align="center"
              gutterBottom
              sx={{
                color: 'primary.main',
                fontWeight: 'bold',
                mb: 6,
              }}
            >
              Open Access Policy
            </Typography>

            {/* Introduction Section */}
            <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
              <Typography variant="h5" gutterBottom color="primary">
                Our Commitment to Open Access
              </Typography>
              <Typography variant="body1" paragraph>
                Lingaya’s Lalita Devi Journal of Professional Studies (LLDJPS) operates as a fully
                open access journal under the <strong>Platinum Open Access</strong> model, meaning
                that authors are not required to pay any article processing charges (APCs) or
                submission fees.
              </Typography>
              <Typography variant="body1" paragraph>
                Upon acceptance, all articles are published under the{' '}
                <strong>Creative Commons Attribution 4.0 International License (CC BY 4.0)</strong>.
                This licensing framework allows for maximum dissemination and reuse of scholarly
                work. Under this license, readers are permitted to share (copy and redistribute the
                material in any medium or format) and adapt (remix, transform, and build upon the
                material) for any purpose, even commercially, provided that proper attribution is
                given to the original author(s) and the source.
              </Typography>
              <Typography variant="body1" paragraph>
                This model ensures that research published in LLDJPS remains freely accessible and
                legally reusable, aligning with the principles of open science and supporting
                greater visibility, impact, and academic collaboration across disciplines.
              </Typography>
            </Paper>

            {/* Benefits Grid */}


            {/* Licensing Information */}
           
          </Container>
        </ThemeProvider>
      </div>
    </>
  );
}

export default OpenAccess;
