import React from 'react'; import Footer from '../../Components/Footer';
import { Container, Typography, Paper, List, ListItem, ListItemIcon, ListItemText } from '@mui/material'; import { Check as CheckIcon } from '@mui/icons-material';
function OtherGuidelines() {
    return (
        <>           
            <div className="min-h-screen bg-orange-50">                <Container maxWidth="lg" sx={{ py: 8 }}>
                <Typography variant="h2"
                    component="h1" align="center"
                    gutterBottom sx={{
                        color: 'primary.main', fontWeight: 'bold',
                        mb: 6,
                    }}
                >                        Additional Guidelines
                </Typography>                    <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
                    <Typography variant="h5" gutterBottom color="primary" fontWeight="bold">                            Supplementary Guidelines for Authors
                    </Typography>                        <Typography paragraph>
                        The Journal of Legal Studies (JLS) provides additional guidelines to ensure the highest quality of submissions and smooth publication process.                        </Typography>
                    <List>                            <ListItem>
                        <ListItemIcon>                                    <CheckIcon color="primary" />
                        </ListItemIcon>                                <ListItemText
                            primary="Language and Writing Style" secondary="Manuscripts should be written in clear, concise academic English following standard legal writing conventions"
                        />                            </ListItem>
                        <ListItem>                                <ListItemIcon>
                            <CheckIcon color="primary" />                                </ListItemIcon>
                            <ListItemText primary="Citation Format"
                                secondary="Authors must follow the Bluebook citation format for legal references" />
                        </ListItem>                            <ListItem>
                            <ListItemIcon>                                    <CheckIcon color="primary" />
                            </ListItemIcon>                                <ListItemText
                                primary="Supplementary Materials" secondary="Any additional materials should be clearly labeled and referenced in the main text"
                            />                            </ListItem>
                    </List>                    </Paper>
            </Container>            </div>
            <Footer />
        </>
    )
}

export default OtherGuidelines;
;
