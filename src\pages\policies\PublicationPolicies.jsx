import React from 'react';
import { Typography, Box, Paper, Grid, List, ListItem, ListItemIcon, ListItemText, Alert, Divider } from '@mui/material';
import { CheckCircle, Warning, Info, Description, Gavel } from '@mui/icons-material';

const PublicationPolicies = () => {
  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h3" component="h1" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 4, textAlign: 'center' }}>
        Publication Policy
      </Typography>

      {/* Introduction */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3 }}>
          International Journal of Advanced Research (IJAR)
        </Typography>
        <Typography variant="body1" sx={{ color: 'text.secondary', mb: 3 }}>
          <strong>ISSN:</strong> 2320-5407 | <strong>Impact Factor:</strong> 7.337 | <strong>IC Value:</strong> 94.33
        </Typography>
        <Typography variant="body1" sx={{ color: 'text.secondary' }}>
          IJAR maintains strict publication policies to ensure the highest standards of academic integrity, 
          research quality, and ethical publishing practices. All authors must adhere to these policies.
        </Typography>
      </Paper>

      {/* General Publication Policy */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3 }}>
          General Publication Policy
        </Typography>
        
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
            Core Principles
          </Typography>
          <Typography variant="body2">
            IJAR is committed to publishing high-quality, original research that contributes significantly 
            to the advancement of knowledge in all fields of study.
          </Typography>
        </Alert>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, color: '#EA7717' }}>
              Publication Criteria
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Originality"
                  secondary="Manuscripts must be original and not previously published"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Scientific Merit"
                  secondary="Research must demonstrate significant scientific contribution"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Methodological Rigor"
                  secondary="Research methods must be sound and appropriate"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Ethical Standards"
                  secondary="Research must comply with ethical guidelines"
                />
              </ListItem>
            </List>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, color: '#EA7717' }}>
              Publication Types
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <Description sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Research Articles"
                  secondary="Original research findings and discoveries"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Description sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Review Articles"
                  secondary="Comprehensive literature reviews and meta-analyses"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Description sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Case Reports"
                  secondary="Clinical or research case studies"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Description sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Short Communications"
                  secondary="Brief research reports and preliminary findings"
                />
              </ListItem>
            </List>
          </Grid>
        </Grid>
      </Paper>

      {/* Review Process Policy */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3 }}>
          Review Process Policy
        </Typography>
        
        <Typography variant="body1" sx={{ color: 'text.secondary', mb: 3 }}>
          IJAR follows a rigorous double-blind peer review process to ensure the quality and integrity of published research.
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
              Review Process Steps
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Initial Screening"
                  secondary="Editorial assessment for basic requirements"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Peer Review"
                  secondary="Expert review by 2-3 qualified reviewers"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Editorial Decision"
                  secondary="Final decision based on reviewer recommendations"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Revision Process"
                  secondary="Author revisions if required"
                />
              </ListItem>
            </List>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
              Review Criteria
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <Gavel sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Scientific Quality"
                  secondary="Rigor of methodology and analysis"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Gavel sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Originality"
                  secondary="Novelty and contribution to field"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Gavel sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Clarity"
                  secondary="Clear presentation and writing quality"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Gavel sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Relevance"
                  secondary="Significance to the field of study"
                />
              </ListItem>
            </List>
          </Grid>
        </Grid>
      </Paper>

      {/* Publication Timeline */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3 }}>
          Publication Timeline
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={3}>
            <Box sx={{ textAlign: 'center', p: 3, border: '1px solid #e0e0e0', borderRadius: 1 }}>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#EA7717' }}>24 Hours</Typography>
              <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 1 }}>Manuscript Number</Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Unique ID assigned
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={3}>
            <Box sx={{ textAlign: 'center', p: 3, border: '1px solid #e0e0e0', borderRadius: 1 }}>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#EA7717' }}>7-10 Days</Typography>
              <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 1 }}>Review Decision</Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Initial review completed
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={3}>
            <Box sx={{ textAlign: 'center', p: 3, border: '1px solid #e0e0e0', borderRadius: 1 }}>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#EA7717' }}>1 Week</Typography>
              <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 1 }}>Peer Review</Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Expert review process
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={3}>
            <Box sx={{ textAlign: 'center', p: 3, border: '1px solid #e0e0e0', borderRadius: 1 }}>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#EA7717' }}>Immediate</Typography>
              <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 1 }}>Online Publication</Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                After acceptance
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Open Access Policy */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3 }}>
          Open Access Policy
        </Typography>
        
        <Typography variant="body1" sx={{ color: 'text.secondary', mb: 3 }}>
          IJAR is an open access journal, which means that all published articles are freely available 
          to readers worldwide without any subscription fees or access restrictions.
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
              Open Access Benefits
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Global Accessibility"
                  secondary="Free access to research worldwide"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Increased Visibility"
                  secondary="Higher citation rates and impact"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Rapid Dissemination"
                  secondary="Immediate availability after publication"
                />
              </ListItem>
            </List>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
              Licensing Terms
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <Info sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Creative Commons License"
                  secondary="CC BY-NC-ND 4.0 International License"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Info sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Author Rights"
                  secondary="Authors retain copyright of their work"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Info sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Usage Permissions"
                  secondary="Non-commercial use with attribution"
                />
              </ListItem>
            </List>
          </Grid>
        </Grid>
      </Paper>

      {/* Publication Ethics */}
      <Paper elevation={2} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3 }}>
          Publication Ethics
        </Typography>
        
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
            Ethical Standards
          </Typography>
          <Typography variant="body2">
            IJAR adheres to the highest ethical standards in academic publishing. All authors, reviewers, 
            and editors must follow these ethical guidelines.
          </Typography>
        </Alert>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
              Author Responsibilities
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <Warning sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Originality"
                  secondary="Ensure work is original and not plagiarized"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Warning sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Data Integrity"
                  secondary="Present accurate and reliable data"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Warning sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Conflict of Interest"
                  secondary="Disclose any potential conflicts"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Warning sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Authorship"
                  secondary="Include only those who contributed significantly"
                />
              </ListItem>
            </List>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
              Editorial Responsibilities
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <Gavel sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Fair Review"
                  secondary="Ensure unbiased and fair review process"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Gavel sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Confidentiality"
                  secondary="Maintain confidentiality of submissions"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Gavel sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Quality Control"
                  secondary="Maintain high standards of publication"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Gavel sx={{ color: '#EA7717' }} />
                </ListItemIcon>
                <ListItemText 
                  primary="Ethical Oversight"
                  secondary="Address ethical concerns promptly"
                />
              </ListItem>
            </List>
          </Grid>
        </Grid>
      </Paper>

      {/* Important Notes */}
      <Alert severity="warning" sx={{ mb: 4 }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
          Important Notes for Authors
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          • All manuscripts must comply with IJAR's publication policies
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          • Violation of ethical guidelines may result in manuscript rejection
        </Typography>
        <Typography variant="body2" sx={{ mb: 1 }}>
          • Authors are responsible for ensuring compliance with all policies
        </Typography>
        <Typography variant="body2">
          • IJAR reserves the right to retract articles that violate policies
        </Typography>
      </Alert>

      {/* Contact Information */}
      <Paper elevation={2} sx={{ p: 4, bgcolor: '#fff8f1', border: '1px solid #EA7717' }}>
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#EA7717', mb: 3, textAlign: 'center' }}>
          Questions About Publication Policy?
        </Typography>
        <Typography variant="body1" sx={{ textAlign: 'center', mb: 3 }}>
          For any queries regarding publication policies or ethical guidelines, contact us:
        </Typography>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
            Email: 
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
            Phone: 
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
            WhatsApp: 
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default PublicationPolicies;
