/* CurrentIssue Component Styles */

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Component-specific styles */
.current-issue-container {
  animation: fadeInUp 0.8s ease-out;
}

.article-card {
  animation: slideInLeft 0.6s ease-out;
  animation-fill-mode: both;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.article-card:nth-child(1) { animation-delay: 0.1s; }
.article-card:nth-child(2) { animation-delay: 0.2s; }
.article-card:nth-child(3) { animation-delay: 0.3s; }
.article-card:nth-child(4) { animation-delay: 0.4s; }
.article-card:nth-child(5) { animation-delay: 0.5s; }
.article-card:nth-child(6) { animation-delay: 0.6s; }
.article-card:nth-child(7) { animation-delay: 0.7s; }

.article-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.article-number {
  background: linear-gradient(135deg, #ea7717 0%, #ff8c42 100%);
  box-shadow: 0 4px 15px rgba(234, 119, 23, 0.3);
  transition: all 0.3s ease;
}

.article-number:hover {
  animation: pulse 0.6s ease-in-out;
  box-shadow: 0 6px 20px rgba(234, 119, 23, 0.4);
}

.download-button {
  background: linear-gradient(135deg, #ea7717 0%, #ff8c42 100%);
  box-shadow: 0 4px 15px rgba(234, 119, 23, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.download-button:hover {
  box-shadow: 0 8px 25px rgba(234, 119, 23, 0.4);
  transform: translateY(-2px) scale(1.05);
}

.download-button:active {
  transform: translateY(0) scale(0.98);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #ea7717 0%, #ff8c42 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom scrollbar */
.current-issue-container::-webkit-scrollbar {
  width: 8px;
}

.current-issue-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.current-issue-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #ea7717 0%, #ff8c42 100%);
  border-radius: 4px;
}

.current-issue-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #d66a15 0%, #e67a3a 100%);
}

/* Responsive typography */
@media (max-width: 768px) {
  .article-card {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
  }
  
  .article-card:nth-child(n) {
    animation-delay: 0.1s;
  }
}

/* Print styles */
@media print {
  .download-button {
    display: none;
  }
  
  .article-card {
    box-shadow: none;
    border: 1px solid #ddd;
    break-inside: avoid;
    margin-bottom: 20px;
  }
  
  .current-issue-container {
    animation: none;
  }
  
  .article-card {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .article-card {
    border: 2px solid #000;
  }
  
  .download-button {
    background: #000;
    color: #fff;
  }
  
  .article-number {
    background: #000;
    color: #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .current-issue-container,
  .article-card,
  .article-number,
  .download-button {
    animation: none;
    transition: none;
  }
  
  .article-card:hover {
    transform: none;
  }
  
  .download-button:hover {
    transform: none;
  }
}

/* Focus styles for accessibility */
.download-button:focus,
.article-card a:focus {
  outline: 3px solid #ea7717;
  outline-offset: 2px;
}

/* Loading state styles */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
