/* Global styles for LLDJPS website */

/* Remove bullets from all lists and ensure left alignment */
ul, ol {
  list-style: none !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
  text-align: left !important;
}

li {
  list-style: none !important;
  text-align: left !important;
}

/* Ensure all text content is left-aligned */
* {
  text-align: left !important;
}

/* Override Material-UI default center alignments */
.MuiTypography-root {
  text-align: left !important;
}

.MuiCardContent-root {
  text-align: left !important;
}

.MuiBox-root {
  text-align: left !important;
}

.MuiContainer-root {
  text-align: left !important;
}

/* Specific overrides for components that should remain centered */
.center-text {
  text-align: center !important;
}

/* Navigation and header elements that should remain centered */
.MuiAppBar-root .MuiTypography-root {
  text-align: center !important;
}

/* Page titles that should remain centered */
h1.page-title,
.page-title {
  text-align: center !important;
}

/* Button text should remain centered */
.MuiButton-root {
  text-align: center !important;
}

/* Form elements should remain centered */
.MuiTextField-root .MuiInputBase-input {
  text-align: left !important;
}

/* Ensure proper spacing and alignment for lists */
.MuiList-root {
  padding-left: 0 !important;
}

.MuiListItem-root {
  padding-left: 0 !important;
  text-align: left !important;
}

.MuiListItemText-root {
  text-align: left !important;
}

/* Remove default browser list styling */
ul::before,
ol::before {
  content: none !important;
}

ul::after,
ol::after {
  content: none !important;
}

/* Ensure sidebar navigation is left-aligned */
.sidebar ul,
.sidebar ol,
.sidebar li {
  text-align: left !important;
  list-style: none !important;
}

/* Ensure dropdown menus are left-aligned */
.dropdown-menu ul,
.dropdown-menu ol,
.dropdown-menu li {
  text-align: left !important;
  list-style: none !important;
}

/* Ensure all paragraphs are left-aligned */
p {
  text-align: left !important;
}

/* Ensure all divs are left-aligned */
div {
  text-align: left !important;
}

/* Ensure all spans are left-aligned */
span {
  text-align: left !important;
}

/* Override any center alignment in components */
.text-center {
  text-align: left !important;
}

/* Specific overrides for justified text */
.text-justify {
  text-align: justify !important;
}

/* Ensure proper alignment for editorial member cards */
.editorial-member-card * {
  text-align: left !important;
}

/* Ensure proper alignment for navigation links */
.nav-link,
.sidebar-link {
  text-align: left !important;
}

/* Ensure proper alignment for footer content */
.footer-content * {
  text-align: left !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  * {
    text-align: left !important;
  }
  
  ul, ol, li {
    text-align: left !important;
    list-style: none !important;
  }
}

/* Animations for mobile menu */
@keyframes fade-in-down {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-down {
  animation: fade-in-down 0.3s ease-out;
}

/* Mobile navbar improvements */
.mobile-menu-overlay {
  backdrop-filter: blur(4px);
  background-color: rgba(0, 0, 0, 0.1);
}

/* Smooth transitions for mobile menu items */
.mobile-menu-item {
  transition: all 0.2s ease-in-out;
}

.mobile-menu-item:hover {
  transform: translateX(4px);
}

/* Print styles */
@media print {
  * {
    text-align: left !important;
  }

  ul, ol {
    list-style: none !important;
  }
}
