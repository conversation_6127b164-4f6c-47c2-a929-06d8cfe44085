/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Custom Color Palette based on #EA7717
        'brand': {
          // Primary Orange Colors (based on #EA7717)
          'orange': {
            50: '#fff8f1',
            100: '#feecdc',
            200: '#fcd9bd',
            300: '#fdba8c',
            400: '#ff8a4c',
            500: '#EA7717', // Main brand color
            600: '#dc2626',
            700: '#b91c1c',
            800: '#991b1b',
            900: '#7f1d1d',
            950: '#450a0a'
          },
          // Complementary Dark Colors
          'dark': {
            50: '#f8fafc',
            100: '#f1f5f9',
            200: '#e2e8f0',
            300: '#cbd5e1',
            400: '#94a3b8',
            500: '#64748b',
            600: '#475569',
            700: '#334155',
            800: '#1e293b',
            900: '#0f172a',
            950: '#020617'
          },
          // Neutral Colors
          'neutral': {
            50: '#fafafa',
            100: '#f5f5f5',
            200: '#e5e5e5',
            300: '#d4d4d4',
            400: '#a3a3a3',
            500: '#737373',
            600: '#525252',
            700: '#404040',
            800: '#262626',
            900: '#171717'
          },
          // Semantic Colors (for easy use)
          'primary': '#EA7717',        // Main brand color
          'primary-light': '#ff8a4c',  // Lighter orange
          'primary-dark': '#b91c1c',   // Darker orange
          'secondary': '#1e293b',      // Dark complement
          'secondary-light': '#475569', // Lighter dark
          'secondary-dark': '#0f172a', // Darker complement
          'text': '#0f172a',           // Dark text
          'text-light': '#525252',     // Gray text
          'background': '#ffffff',      // Pure white
          'surface': '#fafafa',        // Light gray surface
          'border': '#e5e5e5',         // Light border
        }
      },
    },
  },
  plugins: [],
}