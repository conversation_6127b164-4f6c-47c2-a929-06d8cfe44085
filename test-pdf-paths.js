#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

console.log('🔍 Checking PDF paths in built files...\n');

// Read the main index JS file
const indexFiles = fs.readdirSync('dist/assets').filter(f => f.startsWith('index-') && f.endsWith('.js'));

if (indexFiles.length === 0) {
  console.log('❌ No index JS file found');
  process.exit(1);
}

const indexFile = path.join('dist/assets', indexFiles[0]);
const content = fs.readFileSync(indexFile, 'utf8');

// Extract PDF paths
const pdfMatches = content.match(/\/assets\/(current|archive)\/[^"']+\.pdf/g);

if (pdfMatches) {
  console.log('✅ Found PDF references in built file:');
  const uniquePaths = [...new Set(pdfMatches)];
  uniquePaths.forEach(path => console.log(`   ${path}`));
  console.log(`\n📊 Total unique PDF paths: ${uniquePaths.length}\n`);
} else {
  console.log('❌ No PDF paths found in built file\n');
}

// Check actual PDF files
console.log('📁 Checking actual PDF files in dist:\n');

const checkDir = (dir, label) => {
  if (fs.existsSync(dir)) {
    const files = fs.readdirSync(dir).filter(f => f.endsWith('.pdf'));
    console.log(`${label}:`);
    files.forEach(f => console.log(`   ✅ ${f}`));
    console.log(`   Total: ${files.length} files\n`);
    return files;
  }
  return [];
};

const currentPdfs = checkDir('dist/assets/current', 'Current Issue PDFs');
const archivePdfs = checkDir('dist/assets/archive', 'Archive PDFs');
const rootPdfs = checkDir('dist/assets', 'Root Assets PDFs (fallback)');

console.log(`\n📊 Summary:`);
console.log(`   Current: ${currentPdfs.length} PDFs`);
console.log(`   Archive: ${archivePdfs.length} PDFs`);
console.log(`   Fallback: ${rootPdfs.length} PDFs`);
console.log(`   Total: ${currentPdfs.length + archivePdfs.length + rootPdfs.length} PDFs\n`);
