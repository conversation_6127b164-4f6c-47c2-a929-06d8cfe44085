{"routes": [{"src": "/assets/current/(.*\\.pdf)", "dest": "/assets/current/$1", "headers": {"Content-Type": "application/pdf"}}, {"src": "/assets/archive/(.*\\.pdf)", "dest": "/assets/archive/$1", "headers": {"Content-Type": "application/pdf"}}, {"src": "/assets/(.*\\.pdf)", "dest": "/assets/$1", "headers": {"Content-Type": "application/pdf"}}, {"src": "/(.*)", "dest": "/index.html"}], "rewrites": [{"source": "/assets/(.*)", "destination": "/assets/$1"}], "headers": [{"source": "/assets/(.*\\.pdf)", "headers": [{"key": "Content-Type", "value": "application/pdf"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}