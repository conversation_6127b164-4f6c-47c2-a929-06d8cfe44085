import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  base: './',
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    copyPublicDir: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@mui/icons-material'],
          router: ['react-router-dom']
        },
        assetFileNames: (assetInfo) => {
          // Keep PDFs in their original structure without hashing
          if (assetInfo.name && assetInfo.name.endsWith('.pdf')) {
            // Keep original filename with spaces - browsers handle URL encoding
            const fileName = assetInfo.name;
            
            // For current issue PDFs, put them in current subdirectory
            if (fileName.includes('Aastha') || fileName.includes('Abhishek') || 
                fileName.includes('Amit') || fileName.includes('<PERSON><PERSON><PERSON>') || 
                fileName.includes('Murari') || fileName.includes('Satyendra') || 
                fileName.includes('Sharmishtha')) {
              return `assets/current/${fileName}`;
            }
            // For archive PDFs, put them in archive subdirectory
            if (fileName.includes('Volume-')) {
              return `assets/archive/${fileName}`;
            }
            return `assets/${fileName}`;
          }
          return 'assets/[name]-[hash][extname]';
        },
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js'
      }
    },
    sourcemap: false,
    minify: 'esbuild'
  },
  server: {
    port: 3000,
    fs: {
      strict: false
    }
  },
  preview: {
    port: 4173,
    open: true,
    strictPort: false
  },
  publicDir: 'public'
})
